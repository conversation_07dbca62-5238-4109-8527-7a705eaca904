apiVersion: v1
kind: ServiceAccount
metadata:
  name: organisation-service-ai-sa
  namespace: ruh-dev
  labels:
    name: organisation-service-ai-sa
    namespace: ruh-dev
    app: organisation-service-ai
    deployment: organisation-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: organisation-service-ai-dp
  namespace: ruh-dev
  labels:
    name: organisation-service-ai-dp
    namespace: ruh-dev
    app: organisation-service-ai
    serviceaccount: organisation-service-ai-sa
    deployment: organisation-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: organisation-service-ai
      deployment: organisation-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-dev
        app: organisation-service-ai
        deployment: organisation-service-ai-dp
    spec:
      serviceAccountName: organisation-service-ai-sa      
      containers:
      - name: organisation-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50070
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: organisation-service-ai-svc
  namespace: ruh-dev
spec:
  selector:
    app: organisation-service-ai
    deployment: organisation-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50070
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:organisation-service-user-hpa
#   namespace: ruh-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:organisation-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: organisation-service-user-ingress
  namespace: ruh-dev
spec:
  ingressClassName: nginx
  rules:
  - host: organisation-api-ruh-dev.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: organisation-service-ai-svc
            port:
              number: 80





