import os
import grpc
from concurrent import futures
import structlog
import atexit

from app.core.config import settings
from app.modules.organisation.services.organisation import OrganisationService
from app.modules.agents.services.agent import AgentService
from app.modules.connectors.handlers.gdrive.services.google_drive_grpc_service import GoogleDriveGrpcService
from app.modules.connectors.handlers.gdrive.workers.sync_worker import GoogleDriveSyncWorker
from app.modules.connectors.handlers.gdrive.services.neo4j_setup import create_google_drive_constraints, create_google_drive_indexes
from app.modules.connectors.handlers.github.services.github_grpc_service import GitHubGrpcService
from app.modules.connectors.handlers.github.workers.sync_worker import GitHubSyncWorker
from app.modules.connectors.handlers.github.services.neo4j_setup import create_github_constraints, create_github_indexes
from app.grpc_ import organisation_pb2_grpc, agent_graph_pb2_grpc, connector_pb2_grpc

logger = structlog.get_logger()

# Initialize sync workers
drive_sync_worker = GoogleDriveSyncWorker()
github_sync_worker = GitHubSyncWorker()

def serve():
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    
    # Add organisation service to server
    organisation_service = OrganisationService()
    organisation_pb2_grpc.add_OrganisationServiceServicer_to_server(organisation_service, server)
    
    # Add agent service to server
    agent_service = AgentService()
    agent_graph_pb2_grpc.add_AgentGraphServiceServicer_to_server(agent_service, server)
    
    # Add Google Drive service to server
    google_drive_service = GoogleDriveGrpcService()
    connector_pb2_grpc.add_ConnectorServiceServicer_to_server(google_drive_service, server)

    # Add GitHub service to server
    github_service = GitHubGrpcService()
    connector_pb2_grpc.add_ConnectorServiceServicer_to_server(github_service, server)
    
    # Get port from environment or use default
    port = os.getenv('PORT', '50070')
    server.add_insecure_port(f'[::]:{port}')
    
    # Create Neo4j constraints and indexes for connectors
    create_google_drive_constraints()
    create_google_drive_indexes()
    create_github_constraints()
    create_github_indexes()

    # Start sync workers
    drive_sync_worker.start()
    github_sync_worker.start()

    # Register shutdown handlers to stop the workers
    atexit.register(drive_sync_worker.stop)
    atexit.register(github_sync_worker.stop)
    
    # Start server
    server.start()
    logger.info(f"Service started on port {port}")
    
    # Keep thread alive
    server.wait_for_termination()

if __name__ == '__main__':
    serve()