"""
Result Aggregator for Enterprise KG Hybrid Search

This module provides functionality to aggregate and rank search results from different
strategies and sources, creating a unified and optimized search result.
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime
from collections import defaultdict

from .search_schemas import (
    SearchQuery,
    SearchResult,
    GraphContext,
    EntityMatch,
    RelationshipMatch,
    SearchStrategy,
    SearchMetrics
)
from ...constants.entities import get_entity_properties
from ...constants.relationships import get_relationship_description

logger = logging.getLogger(__name__)


class SearchResultAggregator:
    """
    Aggregates and optimizes search results from multiple strategies and sources.
    """

    def __init__(self, enable_advanced_reranking: bool = True):
        """
        Initialize the result aggregator.

        Args:
            enable_advanced_reranking: Whether to enable advanced reranking techniques
        """
        self.entity_deduplication_threshold = 0.9  # Similarity threshold for entity deduplication
        self.relationship_deduplication_threshold = 0.95  # Similarity threshold for relationship deduplication

        # Initialize advanced reranker
        self.enable_advanced_reranking = enable_advanced_reranking
        if enable_advanced_reranking:
            try:
                self.advanced_reranker = None  # Disabled for now
                logger.info("Advanced reranking disabled")
            except Exception as e:
                logger.warning(f"Failed to initialize advanced reranker: {e}")
                self.enable_advanced_reranking = False
                self.advanced_reranker = None
        else:
            self.advanced_reranker = None
            logger.info("Advanced reranking disabled")
    
    def aggregate_results(
        self, 
        results: List[SearchResult], 
        query: SearchQuery
    ) -> SearchResult:
        """
        Aggregate multiple search results into a single optimized result.
        
        Args:
            results: List of search results from different strategies
            query: Original search query
            
        Returns:
            Aggregated and optimized SearchResult
        """
        start_time = datetime.now()
        
        try:
            if not results:
                return self._create_empty_result(query, start_time)
            
            # If only one result, return it with minimal processing
            if len(results) == 1:
                return self._enhance_single_result(results[0], start_time)
            
            # Aggregate entities and relationships
            aggregated_entities = self._aggregate_entities(results)
            aggregated_relationships = self._aggregate_relationships(results)
            
            # Deduplicate and rank
            deduplicated_entities = self._deduplicate_entities(aggregated_entities)
            deduplicated_relationships = self._deduplicate_relationships(aggregated_relationships)
            
            # Rank final results
            ranked_entities = self._rank_entities(deduplicated_entities, query)
            ranked_relationships = self._rank_relationships(deduplicated_relationships, query)

            # Advanced reranking is disabled for now
            logger.debug("Advanced reranking skipped (disabled)")
            
            # Limit results
            final_entities = ranked_entities[:query.max_results]
            final_relationships = ranked_relationships[:query.max_results]
            
            # Build aggregated graph context
            aggregated_context = GraphContext(
                entities=final_entities,
                relationships=final_relationships,
                source_chunks=self._aggregate_source_chunks(results),
                chunk_count=self._count_unique_chunks(results),
                total_entities=len(final_entities),
                total_relationships=len(final_relationships),
                entity_types_found=set(e.entity_type for e in final_entities),
                relationship_types_found=set(r.relationship_type for r in final_relationships),
                max_depth_reached=max((r.graph_context.max_depth_reached for r in results), default=0),
                expansion_paths=self._aggregate_expansion_paths(results)
            )
            
            # Calculate quality scores
            coverage_score = self._calculate_aggregated_coverage_score(aggregated_context, results)
            coherence_score = self._calculate_aggregated_coherence_score(aggregated_context, results)
            relevance_score = self._calculate_aggregated_relevance_score(aggregated_context, results)
            
            # Create final result
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000
            
            aggregated_result = SearchResult(
                query=query,
                graph_context=aggregated_context,
                total_results=len(final_entities) + len(final_relationships),
                processing_time_ms=processing_time,
                strategy_used=SearchStrategy.HYBRID,
                coverage_score=coverage_score,
                coherence_score=coherence_score,
                relevance_score=relevance_score,
                debug_info={
                    "aggregation": "multi_strategy",
                    "strategies_used": [r.strategy_used.value for r in results],
                    "original_results_count": len(results),
                    "entities_before_dedup": len(aggregated_entities),
                    "relationships_before_dedup": len(aggregated_relationships),
                    "entities_after_dedup": len(final_entities),
                    "relationships_after_dedup": len(final_relationships)
                }
            )
            
            logger.info(f"Aggregated {len(results)} search results: {len(final_entities)} entities, "
                       f"{len(final_relationships)} relationships in {processing_time:.2f}ms")
            
            return aggregated_result
            
        except Exception as e:
            logger.error(f"Error aggregating search results: {e}")
            return self._create_error_result(query, str(e), start_time)
    
    def _aggregate_entities(self, results: List[SearchResult]) -> List[EntityMatch]:
        """Aggregate entities from multiple results."""
        all_entities = []
        for result in results:
            all_entities.extend(result.graph_context.entities)
        return all_entities
    
    def _aggregate_relationships(self, results: List[SearchResult]) -> List[RelationshipMatch]:
        """Aggregate relationships from multiple results."""
        all_relationships = []
        for result in results:
            all_relationships.extend(result.graph_context.relationships)
        return all_relationships
    
    def _deduplicate_entities(self, entities: List[EntityMatch]) -> List[EntityMatch]:
        """
        Deduplicate entities based on name and type similarity.

        Args:
            entities: List of entities to deduplicate

        Returns:
            List of deduplicated entities with merged information
        """
        if not entities:
            return []

        # Group entities by (name, type) for exact matches
        entity_groups = defaultdict(list)
        for entity in entities:
            # Handle None values safely
            name = entity.name.lower().strip() if entity.name else "unknown"
            entity_type = entity.entity_type.lower().strip() if entity.entity_type else "unknown"
            key = (name, entity_type)
            entity_groups[key].append(entity)

        deduplicated = []
        for group in entity_groups.values():
            if len(group) == 1:
                deduplicated.append(group[0])
            else:
                # Merge multiple entities with same name/type
                merged_entity = self._merge_entities(group)
                deduplicated.append(merged_entity)

        logger.debug(f"Deduplicated {len(entities)} entities to {len(deduplicated)}")
        return deduplicated
    
    def _merge_entities(self, entities: List[EntityMatch]) -> EntityMatch:
        """
        Merge multiple entities with the same name and type.
        
        Args:
            entities: List of entities to merge
            
        Returns:
            Merged EntityMatch
        """
        if not entities:
            raise ValueError("Cannot merge empty entity list")
        
        if len(entities) == 1:
            return entities[0]
        
        # Use the entity with highest relevance score as base
        base_entity = max(entities, key=lambda e: e.relevance_score or 0.0)
        
        # Merge properties and sources
        merged_chunk_sources = []
        merged_match_reasons = []
        total_relevance = 0.0
        
        for entity in entities:
            merged_chunk_sources.extend(entity.chunk_sources)
            merged_match_reasons.append(entity.match_reason)
            total_relevance += entity.relevance_score
        
        # Create merged entity
        merged_entity = EntityMatch(
            name=base_entity.name,
            entity_type=base_entity.entity_type,
            node_id=base_entity.node_id,
            properties=base_entity.properties,
            relevance_score=min(1.0, total_relevance / len(entities) * 1.2),  # Boost for multiple mentions
            match_reason=" + ".join(set(merged_match_reasons)),
            chunk_sources=list(set(merged_chunk_sources)),
            relationship_count=max((e.relationship_count for e in entities), default=0),
            created_at=min((e.created_at for e in entities if e.created_at), default=None),
            updated_at=max((e.updated_at for e in entities if e.updated_at), default=None)
        )
        
        return merged_entity
    
    def _deduplicate_relationships(self, relationships: List[RelationshipMatch]) -> List[RelationshipMatch]:
        """
        Deduplicate relationships based on source, target, and type.

        Args:
            relationships: List of relationships to deduplicate

        Returns:
            List of deduplicated relationships
        """
        if not relationships:
            return []

        # Group relationships by (source, target, type)
        relationship_groups = defaultdict(list)
        for relationship in relationships:
            # Handle None values safely
            source = relationship.source_entity.lower().strip() if relationship.source_entity else "unknown"
            target = relationship.target_entity.lower().strip() if relationship.target_entity else "unknown"
            rel_type = relationship.relationship_type.lower().strip() if relationship.relationship_type else "unknown"
            key = (source, target, rel_type)
            relationship_groups[key].append(relationship)
        
        deduplicated = []
        for group in relationship_groups.values():
            if len(group) == 1:
                deduplicated.append(group[0])
            else:
                # Merge multiple relationships
                merged_relationship = self._merge_relationships(group)
                deduplicated.append(merged_relationship)
        
        logger.debug(f"Deduplicated {len(relationships)} relationships to {len(deduplicated)}")
        return deduplicated
    
    def _merge_relationships(self, relationships: List[RelationshipMatch]) -> RelationshipMatch:
        """
        Merge multiple relationships with the same source, target, and type.
        
        Args:
            relationships: List of relationships to merge
            
        Returns:
            Merged RelationshipMatch
        """
        if not relationships:
            raise ValueError("Cannot merge empty relationship list")
        
        if len(relationships) == 1:
            return relationships[0]
        
        # Use the relationship with highest confidence as base
        base_relationship = max(relationships, key=lambda r: r.confidence_score or 0.0)
        
        # Merge sources and contexts
        merged_chunk_sources = []
        merged_contexts = []
        merged_match_reasons = []
        total_confidence = 0.0
        total_relevance = 0.0
        
        for relationship in relationships:
            merged_chunk_sources.extend(relationship.chunk_sources)
            if relationship.context:
                merged_contexts.append(relationship.context)
            merged_match_reasons.append(relationship.match_reason)
            total_confidence += relationship.confidence_score
            total_relevance += relationship.relevance_score
        
        # Create merged relationship
        merged_relationship = RelationshipMatch(
            source_entity=base_relationship.source_entity,
            target_entity=base_relationship.target_entity,
            relationship_type=base_relationship.relationship_type,
            properties=base_relationship.properties,
            confidence_score=min(1.0, total_confidence / len(relationships) * 1.1),  # Slight boost for multiple mentions
            context=" | ".join(set(merged_contexts)) if merged_contexts else base_relationship.context,
            source_sentence=base_relationship.source_sentence,
            relevance_score=min(1.0, total_relevance / len(relationships) * 1.2),  # Boost for multiple mentions
            match_reason=" + ".join(set(merged_match_reasons)),
            chunk_sources=list(set(merged_chunk_sources)),
            created_at=min(r.created_at for r in relationships if r.created_at)
        )
        
        return merged_relationship

    def _rank_entities(self, entities: List[EntityMatch], query: SearchQuery) -> List[EntityMatch]:
        """
        Rank entities by relevance and importance.

        Args:
            entities: List of entities to rank
            query: Search query for context

        Returns:
            List of ranked entities
        """
        if not entities:
            return []

        # Apply additional ranking factors
        for entity in entities:
            # Boost for query-specific entity types
            if query.entity_types and entity.entity_type in query.entity_types:
                entity.relevance_score = min(1.0, entity.relevance_score * 1.2)

            # Boost for high importance entities
            if query.boost_high_importance:
                importance = entity.properties.get("graph_importance", 0.5)
                if importance > 0.7:
                    entity.relevance_score = min(1.0, entity.relevance_score * 1.1)

            # Boost for entities with many chunk sources
            if len(entity.chunk_sources) > 1:
                chunk_boost = min(0.2, len(entity.chunk_sources) * 0.05)
                entity.relevance_score = min(1.0, entity.relevance_score + chunk_boost)

        # Sort by relevance score (descending)
        return sorted(entities, key=lambda e: e.relevance_score, reverse=True)

    def _rank_relationships(self, relationships: List[RelationshipMatch], query: SearchQuery) -> List[RelationshipMatch]:
        """
        Rank relationships by relevance and confidence.

        Args:
            relationships: List of relationships to rank
            query: Search query for context

        Returns:
            List of ranked relationships
        """
        if not relationships:
            return []

        # Apply additional ranking factors
        for relationship in relationships:
            # Boost for query-specific relationship types
            if query.relationship_types and relationship.relationship_type in query.relationship_types:
                relationship.relevance_score = min(1.0, relationship.relevance_score * 1.2)

            # Boost for high confidence relationships
            if relationship.confidence_score > 0.7:
                relationship.relevance_score = min(1.0, relationship.relevance_score * 1.1)

            # Boost for relationships with context
            if relationship.context and len(relationship.context) > 10:
                relationship.relevance_score = min(1.0, relationship.relevance_score + 0.1)

        # Sort by relevance score (descending), then by confidence score
        return sorted(
            relationships,
            key=lambda r: (r.relevance_score, r.confidence_score),
            reverse=True
        )

    def _aggregate_source_chunks(self, results: List[SearchResult]) -> List[Dict[str, Any]]:
        """Aggregate source chunks from multiple results."""
        all_chunks = []
        seen_chunk_ids = set()

        for result in results:
            for chunk in result.graph_context.source_chunks:
                chunk_id = chunk.get("chunk_id")
                if chunk_id and chunk_id not in seen_chunk_ids:
                    all_chunks.append(chunk)
                    seen_chunk_ids.add(chunk_id)

        return all_chunks

    def _count_unique_chunks(self, results: List[SearchResult]) -> int:
        """Count unique chunks across all results."""
        unique_chunk_ids = set()
        for result in results:
            for chunk in result.graph_context.source_chunks:
                chunk_id = chunk.get("chunk_id")
                if chunk_id:
                    unique_chunk_ids.add(chunk_id)
        return len(unique_chunk_ids)

    def _aggregate_expansion_paths(self, results: List[SearchResult]) -> List[List[str]]:
        """Aggregate expansion paths from multiple results."""
        all_paths = []
        for result in results:
            all_paths.extend(result.graph_context.expansion_paths)
        return all_paths

    def _calculate_aggregated_coverage_score(
        self,
        context: GraphContext,
        results: List[SearchResult]
    ) -> float:
        """Calculate coverage score for aggregated results."""
        if not results:
            return 0.0

        # Average coverage from individual results
        avg_coverage = sum(r.coverage_score for r in results) / len(results)

        # Boost for diversity in aggregated results
        entity_type_diversity = len(context.entity_types_found) / max(1, len(context.entities))
        relationship_type_diversity = len(context.relationship_types_found) / max(1, len(context.relationships))

        diversity_boost = (entity_type_diversity + relationship_type_diversity) / 2 * 0.2

        return min(1.0, avg_coverage + diversity_boost)

    def _calculate_aggregated_coherence_score(
        self,
        context: GraphContext,
        results: List[SearchResult]
    ) -> float:
        """Calculate coherence score for aggregated results."""
        if not results:
            return 0.0

        # Average coherence from individual results
        avg_coherence = sum(r.coherence_score for r in results) / len(results)

        # Boost for well-connected aggregated graph
        if context.entities and context.relationships:
            connection_density = len(context.relationships) / max(1, len(context.entities))
            connection_boost = min(0.2, connection_density * 0.1)
            avg_coherence = min(1.0, avg_coherence + connection_boost)

        return avg_coherence

    def _calculate_aggregated_relevance_score(
        self,
        context: GraphContext,
        results: List[SearchResult]
    ) -> float:
        """Calculate relevance score for aggregated results."""
        if not results:
            return 0.0

        # Weighted average relevance from individual results
        total_weight = 0.0
        weighted_relevance = 0.0

        for result in results:
            weight = result.total_results  # Weight by number of results
            weighted_relevance += result.relevance_score * weight
            total_weight += weight

        if total_weight == 0:
            return 0.0

        avg_relevance = weighted_relevance / total_weight

        # Boost for high-quality aggregated entities and relationships
        if context.entities:
            high_relevance_entities = sum(1 for e in context.entities if e.relevance_score > 0.7)
            entity_quality_boost = min(0.2, high_relevance_entities / len(context.entities) * 0.3)
            avg_relevance = min(1.0, avg_relevance + entity_quality_boost)

        return avg_relevance

    def _enhance_single_result(
        self,
        result: SearchResult,
        start_time: datetime
    ) -> SearchResult:
        """Enhance a single result with minimal processing."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        # Add aggregation info to debug
        result.debug_info.update({
            "aggregation": "single_result",
            "aggregation_time_ms": processing_time
        })

        return result

    def _create_empty_result(self, query: SearchQuery, start_time: datetime) -> SearchResult:
        """Create an empty result when no results are provided."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        return SearchResult(
            query=query,
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.HYBRID,
            debug_info={"aggregation": "empty_results"}
        )

    def _create_error_result(self, query: SearchQuery, error: str, start_time: datetime) -> SearchResult:
        """Create an error result."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        return SearchResult(
            query=query,
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.HYBRID,
            debug_info={"aggregation_error": error}
        )
