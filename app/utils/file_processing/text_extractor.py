"""
Text extraction utilities for various file formats.
"""

import io
import json
import csv
from typing import Optional
import structlog
from bs4 import BeautifulSoup

logger = structlog.get_logger()

class TextExtractor:
    """
    Extracts text content from various file formats.
    """
    
    def __init__(self):
        self._pdf_available = self._check_pdf_support()
        self._docx_available = self._check_docx_support()
        self._pptx_available = self._check_pptx_support()
        self._excel_available = self._check_excel_support()
    
    def _check_pdf_support(self) -> bool:
        """Check if PDF extraction is available."""
        try:
            import PyPDF2
            return True
        except ImportError:
            try:
                import pypdf
                return True
            except ImportError:
                logger.warning("PyPDF2/pypdf not available, PDF extraction disabled")
                return False
    
    def _check_docx_support(self) -> bool:
        """Check if DOCX extraction is available."""
        try:
            import docx
            return True
        except ImportError:
            logger.warning("python-docx not available, DOCX extraction disabled")
            return False
    
    def _check_pptx_support(self) -> bool:
        """Check if PPTX extraction is available."""
        try:
            import pptx
            return True
        except ImportError:
            logger.warning("python-pptx not available, PPTX extraction disabled")
            return False
    
    def _check_excel_support(self) -> bool:
        """Check if Excel extraction is available."""
        try:
            import openpyxl
            import pandas as pd
            return True
        except ImportError:
            logger.warning("openpyxl/pandas not available, Excel extraction disabled")
            return False
    
    def extract_text_from_pdf(self, content: bytes) -> Optional[str]:
        """
        Extract text from PDF content.
        
        Args:
            content: PDF file content as bytes
            
        Returns:
            Extracted text or None if extraction failed
        """
        if not self._pdf_available:
            return None
        
        try:
            # Try PyPDF2 first, then pypdf
            try:
                import PyPDF2
                pdf_file = io.BytesIO(content)
                pdf_reader = PyPDF2.PdfReader(pdf_file)
            except ImportError:
                import pypdf
                pdf_file = io.BytesIO(content)
                pdf_reader = pypdf.PdfReader(pdf_file)
            
            text_parts = []
            for page in pdf_reader.pages:
                try:
                    text = page.extract_text()
                    if text.strip():
                        text_parts.append(text)
                except Exception as e:
                    logger.warning(f"Error extracting text from PDF page: {str(e)}")
                    continue
            
            return '\n'.join(text_parts) if text_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            return None
    
    def extract_text_from_docx(self, content: bytes) -> Optional[str]:
        """
        Extract text from DOCX content.
        
        Args:
            content: DOCX file content as bytes
            
        Returns:
            Extracted text or None if extraction failed
        """
        if not self._docx_available:
            return None
        
        try:
            import docx
            
            doc_file = io.BytesIO(content)
            doc = docx.Document(doc_file)
            
            text_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_parts.append(cell.text)
            
            return '\n'.join(text_parts) if text_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {str(e)}")
            return None
    
    def extract_text_from_pptx(self, content: bytes) -> Optional[str]:
        """
        Extract text from PPTX content.
        
        Args:
            content: PPTX file content as bytes
            
        Returns:
            Extracted text or None if extraction failed
        """
        if not self._pptx_available:
            return None
        
        try:
            import pptx
            
            ppt_file = io.BytesIO(content)
            presentation = pptx.Presentation(ppt_file)
            
            text_parts = []
            for slide in presentation.slides:
                for shape in slide.shapes:
                    if hasattr(shape, 'text') and shape.text.strip():
                        text_parts.append(shape.text)
            
            return '\n'.join(text_parts) if text_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting text from PPTX: {str(e)}")
            return None
    
    def extract_text_from_excel(self, content: bytes, mime_type: str) -> Optional[str]:
        """
        Extract text from Excel content.
        
        Args:
            content: Excel file content as bytes
            mime_type: MIME type to determine format
            
        Returns:
            Extracted text or None if extraction failed
        """
        if not self._excel_available:
            return None
        
        try:
            import pandas as pd
            
            excel_file = io.BytesIO(content)
            
            # Determine engine based on MIME type
            if mime_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                engine = 'openpyxl'
            else:
                engine = 'xlrd'
            
            # Read all sheets
            text_parts = []
            try:
                excel_data = pd.read_excel(excel_file, sheet_name=None, engine=engine)
                
                for sheet_name, df in excel_data.items():
                    # Convert DataFrame to string representation
                    sheet_text = df.to_string(index=False, na_rep='')
                    if sheet_text.strip():
                        text_parts.append(f"Sheet: {sheet_name}\n{sheet_text}")
                
            except Exception as e:
                logger.warning(f"Error reading Excel with pandas: {str(e)}")
                return None
            
            return '\n\n'.join(text_parts) if text_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting text from Excel: {str(e)}")
            return None
    
    def extract_text_from_csv(self, content: bytes) -> Optional[str]:
        """
        Extract text from CSV content.
        
        Args:
            content: CSV file content as bytes
            
        Returns:
            Extracted text or None if extraction failed
        """
        try:
            # Try to decode as UTF-8 first
            try:
                text_content = content.decode('utf-8')
            except UnicodeDecodeError:
                # Try other encodings
                for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        text_content = content.decode(encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    return None
            
            # Parse CSV and convert to readable format
            csv_file = io.StringIO(text_content)
            reader = csv.reader(csv_file)
            
            rows = []
            for row in reader:
                if any(cell.strip() for cell in row):  # Skip empty rows
                    rows.append('\t'.join(row))
            
            return '\n'.join(rows) if rows else None
            
        except Exception as e:
            logger.error(f"Error extracting text from CSV: {str(e)}")
            return None
    
    def extract_text_from_html(self, content: bytes) -> Optional[str]:
        """
        Extract text from HTML content.
        
        Args:
            content: HTML file content as bytes
            
        Returns:
            Extracted text or None if extraction failed
        """
        try:
            # Try to decode as UTF-8 first
            try:
                html_content = content.decode('utf-8')
            except UnicodeDecodeError:
                # Try other encodings
                for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        html_content = content.decode(encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    return None
            
            # Parse HTML and extract text
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text and clean it up
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text if text.strip() else None
            
        except Exception as e:
            logger.error(f"Error extracting text from HTML: {str(e)}")
            return None
    
    def extract_text_from_plain_text(self, content: bytes) -> Optional[str]:
        """
        Extract text from plain text content.
        
        Args:
            content: Text file content as bytes
            
        Returns:
            Extracted text or None if extraction failed
        """
        try:
            # Try to decode as UTF-8 first
            try:
                return content.decode('utf-8')
            except UnicodeDecodeError:
                # Try other encodings
                for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        return content.decode(encoding)
                    except UnicodeDecodeError:
                        continue
                
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text from plain text: {str(e)}")
            return None
    
    def extract_text_from_json(self, content: bytes) -> Optional[str]:
        """
        Extract text from JSON content.
        
        Args:
            content: JSON file content as bytes
            
        Returns:
            Extracted text or None if extraction failed
        """
        try:
            # Decode content
            try:
                json_content = content.decode('utf-8')
            except UnicodeDecodeError:
                return None
            
            # Parse JSON
            data = json.loads(json_content)
            
            # Convert to readable format
            return json.dumps(data, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Error extracting text from JSON: {str(e)}")
            return None
    
    def extract_text(self, content: bytes, mime_type: str) -> Optional[str]:
        """
        Extract text from content based on MIME type.
        
        Args:
            content: File content as bytes
            mime_type: MIME type of the content
            
        Returns:
            Extracted text or None if extraction failed
        """
        if not content:
            return None
        
        try:
            if mime_type == 'application/pdf':
                return self.extract_text_from_pdf(content)
            elif mime_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return self.extract_text_from_docx(content)
            elif mime_type == 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                return self.extract_text_from_pptx(content)
            elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
                return self.extract_text_from_excel(content, mime_type)
            elif mime_type == 'text/csv':
                return self.extract_text_from_csv(content)
            elif mime_type == 'text/html':
                return self.extract_text_from_html(content)
            elif mime_type == 'application/json':
                return self.extract_text_from_json(content)
            elif mime_type in ['text/plain', 'text/markdown', 'application/xml', 'text/xml']:
                return self.extract_text_from_plain_text(content)
            else:
                # Try as plain text as fallback
                return self.extract_text_from_plain_text(content)
                
        except Exception as e:
            logger.error(f"Error extracting text for MIME type {mime_type}: {str(e)}")
            return None