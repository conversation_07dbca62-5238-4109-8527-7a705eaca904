"""
URL validation utilities for security and accessibility checks.
"""

import re
import requests
from typing import Tuple
import structlog

logger = structlog.get_logger()

class URLValidator:
    """
    Validates URLs for security and accessibility.
    """
    
    # Maximum file size: 50MB
    MAX_FILE_SIZE = 50 * 1024 * 1024
    
    # Blocked patterns for security
    BLOCKED_PATTERNS = [
        'localhost',
        '127.0.0.1',
        '0.0.0.0',
        '10.',
        '192.168.',
        '172.16.',
        'file://',
        'ftp://',
        'data:',
        'javascript:'
    ]
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'OrganisationService/1.0 (File Processor)'
        })
    
    def validate_url(self, url: str) -> Tuple[bool, str]:
        """
        Validate URL for security and accessibility.
        
        Args:
            url: The URL to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # 1. Check URL format
            if not url.startswith(('http://', 'https://')):
                return False, "Only HTTP/HTTPS URLs are supported"
            
            # 2. Check for malicious patterns
            url_lower = url.lower()
            for pattern in self.BLOCKED_PATTERNS:
                if pattern in url_lower:
                    return False, f"URL contains blocked pattern: {pattern}"
            
            # 3. Check URL accessibility with HEAD request
            try:
                response = self.session.head(url, timeout=10, allow_redirects=True)
                
                # Check status code
                if response.status_code not in [200, 301, 302]:
                    return False, f"URL not accessible: HTTP {response.status_code}"
                
                # Check content length if provided
                content_length = response.headers.get('content-length')
                if content_length:
                    try:
                        size = int(content_length)
                        if size > self.MAX_FILE_SIZE:
                            return False, f"File too large: {size} bytes (max: {self.MAX_FILE_SIZE})"
                    except ValueError:
                        pass  # Invalid content-length header, ignore
                
                return True, "URL is valid"
                
            except requests.exceptions.Timeout:
                return False, "URL request timed out"
            except requests.exceptions.ConnectionError:
                return False, "Could not connect to URL"
            except requests.exceptions.RequestException as e:
                return False, f"URL validation failed: {str(e)}"
                
        except Exception as e:
            logger.error(f"Error validating URL: {str(e)}")
            return False, f"URL validation error: {str(e)}"
    
    def is_google_drive_url(self, url: str) -> bool:
        """
        Check if URL is a Google Drive URL.
        
        Args:
            url: The URL to check
            
        Returns:
            True if it's a Google Drive URL
        """
        google_drive_patterns = [
            r'https://drive\.google\.com/file/d/([a-zA-Z0-9-_]+)',
            r'https://drive\.google\.com/open\?id=([a-zA-Z0-9-_]+)',
            r'https://docs\.google\.com/document/d/([a-zA-Z0-9-_]+)',
            r'https://docs\.google\.com/spreadsheets/d/([a-zA-Z0-9-_]+)',
            r'https://docs\.google\.com/presentation/d/([a-zA-Z0-9-_]+)',
            r'https://drive\.google\.com/drive/folders/([a-zA-Z0-9-_]+)'
        ]
        
        for pattern in google_drive_patterns:
            if re.match(pattern, url):
                return True
        
        return False
    
    def detect_url_type(self, url: str) -> str:
        """
        Detect the type of URL.
        
        Args:
            url: The URL to analyze
            
        Returns:
            'google_drive', 'generic_url', or 'unknown'
        """
        if self.is_google_drive_url(url):
            return "google_drive"
        elif url.startswith(('http://', 'https://')):
            return "generic_url"
        else:
            return "unknown"
    def validate_and_classify_url(self, url: str) -> Tuple[bool, str, str]:
        """
        Validate URL and classify its type.
        
        Args:
            url: The URL to validate and classify
            
        Returns:
            Tuple of (is_valid, url_type, error_message)
        """
        # First validate the URL
        is_valid, error_msg = self.validate_url(url)
        
        if not is_valid:
            return False, '', error_msg
        
        # Then classify the URL type
        url_type = self.detect_url_type(url)
        
        # Normalize the url_type to match expected values
        if url_type == "generic_url":
            url_type = "generic"
        
        return True, url_type, ''