"""
Entity validation utilities for Enterprise KG

This module provides entity type validation and compatibility checking
for the enhanced knowledge graph builder.
"""

import logging
from typing import Set, Dict, List
from app.constants.entities import (
    EntityType, 
    get_all_entity_types,
    get_person_related_types,
    get_organization_related_types,
    get_project_related_types,
    get_system_related_types,
    get_document_related_types,
    get_financial_related_types,
    get_location_related_types
)
from app.constants.relationships import RelationshipType, get_all_relationship_types
from app.constants.schemas import EntityRelationship

logger = logging.getLogger(__name__)


class EntityValidator:
    """
    Validates entity types and relationship compatibility for knowledge graph construction.
    """
    
    def __init__(self):
        """Initialize the entity validator with compatibility groups."""
        self.valid_entity_types = get_all_entity_types()
        self.valid_relationship_types = get_all_relationship_types()
        
        # Define compatible entity type groups
        self.compatibility_groups = [
            get_person_related_types(),
            get_organization_related_types(),
            get_project_related_types(),
            get_system_related_types(),
            get_document_related_types(),
            get_financial_related_types(),
            get_location_related_types()
        ]
    
    def validate_entity_type(self, entity_type: str) -> str:
        """
        Validate and normalize entity type.
        
        Args:
            entity_type: The entity type to validate
            
        Returns:
            Validated entity type or fallback to 'Entity'
        """
        if not entity_type:
            logger.warning("Empty entity type provided, using fallback")
            return EntityType.ENTITY.value
        
        # Check if it's a valid entity type
        if entity_type in self.valid_entity_types:
            return entity_type
        
        # Try to find a close match (case insensitive)
        entity_type_lower = entity_type.lower()
        for valid_type in self.valid_entity_types:
            if valid_type.lower() == entity_type_lower:
                logger.debug(f"Normalized entity type: {entity_type} -> {valid_type}")
                return valid_type
        
        # Log warning and use fallback
        logger.warning(f"Invalid entity type '{entity_type}', using fallback 'Entity'")
        return EntityType.ENTITY.value
    
    def validate_relationship_type(self, relationship_type: str) -> str:
        """
        Validate and normalize relationship type.
        
        Args:
            relationship_type: The relationship type to validate
            
        Returns:
            Validated relationship type or fallback to 'RELATED_TO'
        """
        if not relationship_type:
            logger.warning("Empty relationship type provided, using fallback")
            return RelationshipType.RELATED_TO.value
        
        # Normalize to uppercase
        relationship_type_upper = relationship_type.upper()
        
        # Check if it's a valid relationship type
        if relationship_type_upper in self.valid_relationship_types:
            return relationship_type_upper
        
        # Log warning and use fallback
        logger.warning(f"Invalid relationship type '{relationship_type}', using fallback 'RELATED_TO'")
        return RelationshipType.RELATED_TO.value
    
    def validate_entity_relationship_compatibility(self, relationship: EntityRelationship) -> bool:
        """
        Validate that entities in a relationship are compatible for connection.
        
        This ensures we don't create relationships between incompatible entity types
        (e.g., "Apple" the company vs "Apple" the fruit).
        
        Args:
            relationship: Entity relationship to validate
            
        Returns:
            True if entities are compatible for relationship
        """
        try:
            # If either entity type is missing, allow the relationship
            if not relationship.subject_type or not relationship.object_type:
                return True
            
            # Validate entity types first
            subject_type = self.validate_entity_type(relationship.subject_type)
            object_type = self.validate_entity_type(relationship.object_type)
            
            # Check if both types are in the same compatibility group
            for group in self.compatibility_groups:
                if subject_type in group and object_type in group:
                    return True
            
            # Special case: Entity type can connect to anything
            if subject_type == EntityType.ENTITY.value or object_type == EntityType.ENTITY.value:
                return True
            
            # Check for cross-group relationships that make sense
            if self._is_valid_cross_group_relationship(subject_type, object_type, relationship.predicate):
                return True
            
            # If no compatibility found, log warning but allow (conservative approach)
            logger.warning(f"Potential entity type mismatch: {relationship.subject} ({subject_type}) "
                         f"-> {relationship.object} ({object_type})")
            return True
            
        except Exception as e:
            logger.error(f"Error validating entity compatibility: {e}")
            return True  # Conservative: allow relationship if validation fails
    
    def _is_valid_cross_group_relationship(self, subject_type: str, object_type: str, predicate: str) -> bool:
        """
        Check if a cross-group relationship is valid.
        
        Args:
            subject_type: Subject entity type
            object_type: Object entity type
            predicate: Relationship predicate
            
        Returns:
            True if the cross-group relationship is valid
        """
        # Define valid cross-group relationships
        valid_cross_relationships = {
            # People can work for organizations
            ("Person", "Company"): ["WORKS_FOR", "EMPLOYED_BY"],
            ("Employee", "Company"): ["WORKS_FOR", "EMPLOYED_BY"],
            ("Manager", "Company"): ["WORKS_FOR", "EMPLOYED_BY"],
            
            # People can be located in places
            ("Person", "Office"): ["LOCATED_IN", "BASED_IN"],
            ("Employee", "Office"): ["LOCATED_IN", "BASED_IN"],
            
            # Projects can use systems
            ("Project", "System"): ["USES", "DEPENDS_ON"],
            ("Project", "Application"): ["USES", "DEPENDS_ON"],
            
            # Documents can reference anything
            ("Document", "Project"): ["DOCUMENTS", "REFERENCES"],
            ("Document", "System"): ["DOCUMENTS", "REFERENCES"],
            ("Document", "Person"): ["AUTHORED_BY", "REFERENCES"],
            
            # Financial relationships
            ("Budget", "Project"): ["ALLOCATED_TO", "FUNDS"],
            ("Cost", "Project"): ["ASSOCIATED_WITH"],
        }
        
        # Check both directions
        key1 = (subject_type, object_type)
        key2 = (object_type, subject_type)
        
        valid_predicates = valid_cross_relationships.get(key1, []) + valid_cross_relationships.get(key2, [])
        
        return predicate.upper() in [p.upper() for p in valid_predicates]
    
    def get_entity_label(self, entity_type: str) -> str:
        """
        Get the Neo4j label for an entity type.
        
        Args:
            entity_type: The entity type
            
        Returns:
            Neo4j label (same as entity type for specific typing)
        """
        validated_type = self.validate_entity_type(entity_type)
        return validated_type
    
    def suggest_entity_type(self, entity_name: str, context: str = "") -> str:
        """
        Suggest an entity type based on entity name and context.
        
        Args:
            entity_name: The name of the entity
            context: Additional context about the entity
            
        Returns:
            Suggested entity type
        """
        entity_name_lower = entity_name.lower()
        context_lower = context.lower()
        
        # Simple heuristics for entity type suggestion
        if any(word in entity_name_lower for word in ["team", "group", "squad"]):
            return EntityType.TEAM.value
        elif any(word in entity_name_lower for word in ["department", "dept", "division"]):
            return EntityType.DEPARTMENT.value
        elif any(word in entity_name_lower for word in ["project", "initiative", "program"]):
            return EntityType.PROJECT.value
        elif any(word in entity_name_lower for word in ["system", "platform", "application", "app"]):
            return EntityType.SYSTEM.value
        elif any(word in entity_name_lower for word in ["manager", "director", "lead", "head"]):
            return EntityType.MANAGER.value
        elif any(word in entity_name_lower for word in ["company", "corp", "inc", "ltd"]):
            return EntityType.COMPANY.value
        elif any(word in context_lower for word in ["manages", "leads", "supervises"]):
            return EntityType.MANAGER.value
        elif any(word in context_lower for word in ["works for", "employee", "staff"]):
            return EntityType.EMPLOYEE.value
        else:
            # Default to Person for names that look like people
            if len(entity_name.split()) >= 2 and entity_name.replace(" ", "").isalpha():
                return EntityType.PERSON.value
            return EntityType.ENTITY.value