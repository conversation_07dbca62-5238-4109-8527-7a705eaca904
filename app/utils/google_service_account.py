import json
import structlog
from typing import Tuple, List, Dict, Any, Optional
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

logger = structlog.get_logger()

class GoogleServiceAccountManager:
    """Manager for Google Service Account operations."""
    
    def __init__(self):
        self.scopes = [
            'https://www.googleapis.com/auth/drive.readonly',
            'https://www.googleapis.com/auth/drive.metadata.readonly'
        ]
    
    def authenticate_service_account(self, credentials_json: str) -> service_account.Credentials:
        """
        Create service account credentials from JSON.
        
        Args:
            credentials_json: Service account key JSON string
            
        Returns:
            Google service account credentials
            
        Raises:
            ValueError: If credentials JSON is invalid
            Exception: If authentication fails
        """
        try:
            credentials_info = json.loads(credentials_json)
            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=self.scopes
            )
            return credentials
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid service account JSON: {str(e)}")
        except Exception as e:
            raise Exception(f"Service account authentication failed: {str(e)}")
    
    def validate_service_account_access(self, credentials_json: str) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Validate service account and return accessible folders.
        
        Args:
            credentials_json: Service account key JSON string
            
        Returns:
            Tuple containing:
            - success: Boolean indicating validation success
            - message: Status message
            - folders: List of accessible top-level folders
        """
        try:
            credentials = self.authenticate_service_account(credentials_json)
            service = build('drive', 'v3', credentials=credentials)
            
            # Test basic access
            about = service.about().get(fields="user").execute()
            service_account_email = about.get('user', {}).get('emailAddress', 'Unknown')
            
            # Get folders shared with service account (top-level only)
            folders = self._get_top_level_shared_folders(service)
            
            message = f"Service account validated successfully. Email: {service_account_email}. Found {len(folders)} accessible folders."
            return True, message, folders
            
        except ValueError as e:
            return False, str(e), []
        except HttpError as e:
            return False, f"Google Drive API error: {str(e)}", []
        except Exception as e:
            logger.error(f"Service account validation error: {str(e)}")
            return False, f"Service account validation failed: {str(e)}", []
    
    def _get_top_level_shared_folders(self, service) -> List[Dict[str, Any]]:
        """
        Get top-level folders shared with the service account.
        
        Args:
            service: Authenticated Google Drive service
            
        Returns:
            List of folder dictionaries with id, name, and metadata
        """
        try:
            # Query for folders shared with the service account
            query = "mimeType='application/vnd.google-apps.folder' and sharedWithMe=true"
            
            results = service.files().list(
                q=query,
                fields="files(id, name, parents, owners, permissions(emailAddress, role, type), createdTime, modifiedTime)",
                pageSize=100
            ).execute()
            
            all_folders = results.get('files', [])
            
            # Filter to get only top-level folders (no parents or parent is root)
            top_level_folders = []
            for folder in all_folders:
                parents = folder.get('parents', [])
                # Consider it top-level if no parents or parent is root/drive
                if not parents or any(parent in ['root', 'drive'] for parent in parents):
                    top_level_folders.append({
                        'id': folder['id'],
                        'name': folder['name'],
                        'created_time': folder.get('createdTime'),
                        'modified_time': folder.get('modifiedTime'),
                        'owners': [owner.get('emailAddress') for owner in folder.get('owners', [])],
                        'permissions': folder.get('permissions', []),
                        'permissions_count': len(folder.get('permissions', []))
                    })
            
            return top_level_folders
            
        except Exception as e:
            logger.error(f"Error fetching shared folders: {str(e)}")
            return []
    
    def get_authenticated_drive_service(self, credentials_json: str):
        """
        Get authenticated Google Drive service using service account.
        
        Args:
            credentials_json: Service account key JSON string
            
        Returns:
            Authenticated Google Drive service
        """
        credentials = self.authenticate_service_account(credentials_json)
        return build('drive', 'v3', credentials=credentials)