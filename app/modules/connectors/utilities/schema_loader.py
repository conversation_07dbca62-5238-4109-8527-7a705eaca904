import os
import yaml
import structlog
from pathlib import Path
from typing import Dict, Any, Optional, List

logger = structlog.get_logger()

class ConnectorSchemaError(Exception):
    """Custom exception for schema-related errors."""
    pass

class ConnectorSchema:
    """
    Generic schema loader for connector modules.
    Supports loading and validating schemas for different connectors.
    """
    
    def __init__(self, connector_name: str, schema_path: Optional[str] = None):
        """
        Initialize schema loader for a specific connector.
        
        Args:
            connector_name: Name of the connector (e.g., 'google_drive', 'slack')
            schema_path: Optional custom path to schema file
            
        Raises:
            ConnectorSchemaError: If schema file cannot be loaded or is invalid
        """
        self.connector_name = connector_name
        
        if schema_path is None:
            schema_path = os.path.join(
                os.path.dirname(__file__), 
                f"{connector_name}_schema.yml"
            )
        
        self.schema_path = schema_path
        self._load_schema()
        self._validate_schema()
    
    def _load_schema(self) -> None:
        """Load schema from YAML file."""
        try:
            if not os.path.exists(self.schema_path):
                raise ConnectorSchemaError(f"Schema file not found: {self.schema_path}")
            
            with open(self.schema_path, 'r') as file:
                self.schema = yaml.safe_load(file)
                
            logger.info(f"Loaded schema for {self.connector_name}", 
                       schema_path=self.schema_path)
                       
        except yaml.YAMLError as e:
            raise ConnectorSchemaError(f"Invalid YAML in schema file: {e}")
        except Exception as e:
            raise ConnectorSchemaError(f"Error loading schema: {e}")
    
    def _validate_schema(self) -> None:
        """Validate schema structure."""
        required_keys = ['version', 'nodes', 'relationships']
        
        for key in required_keys:
            if key not in self.schema:
                raise ConnectorSchemaError(f"Missing required key '{key}' in schema")
        
        if not isinstance(self.schema['nodes'], dict):
            raise ConnectorSchemaError("'nodes' must be a dictionary")
            
        if not isinstance(self.schema['relationships'], dict):
            raise ConnectorSchemaError("'relationships' must be a dictionary")
    
    def get_node_labels(self) -> List[str]:
        """Get all node labels from schema."""
        return list(self.schema["nodes"].keys())
    
    def get_relationship_types(self) -> List[str]:
        """Get all relationship types from schema."""
        return list(self.schema["relationships"].keys())
    
    def get_node_properties(self, label: str) -> Dict[str, Any]:
        """Get properties for a specific node label."""
        return self.schema["nodes"].get(label, {}).get("properties", {})
    
    def get_relationship_properties(self, rel_type: str) -> Dict[str, Any]:
        """Get properties for a specific relationship type."""
        return self.schema["relationships"].get(rel_type, {}).get("properties", {})
    
    def get_relationship_config(self, rel_type: str) -> Dict[str, Any]:
        """Get full configuration for a relationship type."""
        return self.schema["relationships"].get(rel_type, {})
    
    def validate_node_properties(self, label: str, properties: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        Validate node properties against schema.
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        schema_props = self.get_node_properties(label)
        
        # Check required properties
        for prop_name, prop_config in schema_props.items():
            if isinstance(prop_config, dict) and prop_config.get("required", False):
                if prop_name not in properties:
                    errors.append(f"Missing required property '{prop_name}' for node '{label}'")
        
        # Check property types (basic validation)
        for prop_name, value in properties.items():
            if prop_name in schema_props:
                prop_config = schema_props[prop_name]
                expected_type = prop_config.get('type') if isinstance(prop_config, dict) else prop_config
                
                if expected_type == 'string' and not isinstance(value, str):
                    errors.append(f"Property '{prop_name}' should be string, got {type(value).__name__}")
                elif expected_type == 'integer' and not isinstance(value, int):
                    errors.append(f"Property '{prop_name}' should be integer, got {type(value).__name__}")
        
        return len(errors) == 0, errors
    
    def get_schema_version(self) -> str:
        """Get schema version."""
        return str(self.schema.get('version', '1.0'))
    
    def get_schema_description(self) -> str:
        """Get schema description."""
        return self.schema.get('description', f'Schema for {self.connector_name} connector')