"""
Connector Constants

This module provides constant definitions for connectors including:
- Standard schemas and data models
- Entity type definitions
- Relationship type definitions
- Configuration constants
"""

from .schemas import (
    ConnectorSearchResponse,
    SearchResultItem,
    SearchStatus,
    SearchMetrics,
    ConnectorConfig,
    ConnectorMetadata
)
from .entities import StandardEntityType, get_entity_category
from .relationships import StandardRelationshipType, get_relationship_category

__all__ = [
    'ConnectorSearchResponse',
    'SearchResultItem',
    'SearchStatus', 
    'SearchMetrics',
    'ConnectorConfig',
    'ConnectorMetadata',
    'StandardEntityType',
    'get_entity_category',
    'StandardRelationshipType',
    'get_relationship_category'
]