"""
Standard Relationship Types for Unstructured Connectors

This module defines common relationship types used by unstructured connectors
like Google Drive, SharePoint, file systems, etc.
"""

from enum import Enum


class StandardRelationshipType(Enum):
    """
    Standard relationship types for unstructured data connectors.
    
    These are common relationship types that can be used across different
    unstructured data sources to represent connections between entities.
    """
    
    # Hierarchical relationships
    CONTAINS = "CONTAINS"
    BELONGS_TO = "BELONGS_TO"
    PARENT_OF = "PARENT_OF"
    CHILD_OF = "CHILD_OF"
    
    # Content relationships
    PART_OF = "PART_OF"
    COMPOSED_OF = "COMPOSED_OF"
    INCLUDES = "INCLUDES"
    REFERENCES = "REFERENCES"
    LINKS_TO = "LINKS_TO"
    
    # Authorship and ownership
    CREATED_BY = "CREATED_BY"
    AUTHORED_BY = "AUTHORED_BY"
    OWNED_BY = "OWNED_BY"
    EDITED_BY = "EDITED_BY"
    REVIEWED_BY = "REVIEWED_BY"
    MODIFIED_BY = "MODIFIED_BY"
    
    # Access and permissions
    HAS_ACCESS = "HAS_ACCESS"
    CAN_READ = "CAN_READ"
    CAN_WRITE = "CAN_WRITE"
    CAN_DELETE = "CAN_DELETE"
    CAN_SHARE = "CAN_SHARE"
    SHARED_WITH = "SHARED_WITH"
    
    # Version and history
    VERSION_OF = "VERSION_OF"
    PREVIOUS_VERSION = "PREVIOUS_VERSION"
    NEXT_VERSION = "NEXT_VERSION"
    DERIVED_FROM = "DERIVED_FROM"
    SUPERSEDES = "SUPERSEDES"
    
    # Similarity and relationships
    SIMILAR_TO = "SIMILAR_TO"
    RELATED_TO = "RELATED_TO"
    DUPLICATE_OF = "DUPLICATE_OF"
    VARIANT_OF = "VARIANT_OF"
    
    # Temporal relationships
    CREATED_BEFORE = "CREATED_BEFORE"
    CREATED_AFTER = "CREATED_AFTER"
    MODIFIED_BEFORE = "MODIFIED_BEFORE"
    MODIFIED_AFTER = "MODIFIED_AFTER"
    
    # Classification and tagging
    TAGGED_WITH = "TAGGED_WITH"
    CATEGORIZED_AS = "CATEGORIZED_AS"
    CLASSIFIED_AS = "CLASSIFIED_AS"
    
    # Content extraction relationships
    EXTRACTED_FROM = "EXTRACTED_FROM"
    MENTIONS = "MENTIONS"
    DISCUSSES = "DISCUSSES"
    ABOUT = "ABOUT"
    
    # Workflow relationships
    DEPENDS_ON = "DEPENDS_ON"
    PREREQUISITE_FOR = "PREREQUISITE_FOR"
    FOLLOWS = "FOLLOWS"
    PRECEDES = "PRECEDES"
    
    # Collaboration relationships
    COLLABORATED_ON = "COLLABORATED_ON"
    CONTRIBUTED_TO = "CONTRIBUTED_TO"
    COMMENTED_ON = "COMMENTED_ON"
    APPROVED_BY = "APPROVED_BY"
    
    # Location and organization
    LOCATED_IN = "LOCATED_IN"
    STORED_IN = "STORED_IN"
    BACKED_UP_TO = "BACKED_UP_TO"
    SYNCED_WITH = "SYNCED_WITH"


def get_all_standard_relationship_types():
    """
    Returns all standard relationship types for unstructured connectors.
    
    Returns:
        set: Set of all standard relationship type string values
    """
    return {r.value for r in StandardRelationshipType}


def get_hierarchical_relationship_types():
    """
    Returns hierarchical relationship types.
    
    Returns:
        set: Set of hierarchical relationship type string values
    """
    return {
        StandardRelationshipType.CONTAINS.value,
        StandardRelationshipType.BELONGS_TO.value,
        StandardRelationshipType.PARENT_OF.value,
        StandardRelationshipType.CHILD_OF.value
    }


def get_content_relationship_types():
    """
    Returns content-related relationship types.
    
    Returns:
        set: Set of content relationship type string values
    """
    return {
        StandardRelationshipType.PART_OF.value,
        StandardRelationshipType.COMPOSED_OF.value,
        StandardRelationshipType.INCLUDES.value,
        StandardRelationshipType.REFERENCES.value,
        StandardRelationshipType.LINKS_TO.value
    }


def get_authorship_relationship_types():
    """
    Returns authorship and ownership relationship types.
    
    Returns:
        set: Set of authorship relationship type string values
    """
    return {
        StandardRelationshipType.CREATED_BY.value,
        StandardRelationshipType.AUTHORED_BY.value,
        StandardRelationshipType.OWNED_BY.value,
        StandardRelationshipType.EDITED_BY.value,
        StandardRelationshipType.REVIEWED_BY.value,
        StandardRelationshipType.MODIFIED_BY.value
    }


def get_access_relationship_types():
    """
    Returns access and permission relationship types.
    
    Returns:
        set: Set of access relationship type string values
    """
    return {
        StandardRelationshipType.HAS_ACCESS.value,
        StandardRelationshipType.CAN_READ.value,
        StandardRelationshipType.CAN_WRITE.value,
        StandardRelationshipType.CAN_DELETE.value,
        StandardRelationshipType.CAN_SHARE.value,
        StandardRelationshipType.SHARED_WITH.value
    }


def get_version_relationship_types():
    """
    Returns version and history relationship types.
    
    Returns:
        set: Set of version relationship type string values
    """
    return {
        StandardRelationshipType.VERSION_OF.value,
        StandardRelationshipType.PREVIOUS_VERSION.value,
        StandardRelationshipType.NEXT_VERSION.value,
        StandardRelationshipType.DERIVED_FROM.value,
        StandardRelationshipType.SUPERSEDES.value
    }


def get_temporal_relationship_types():
    """
    Returns temporal relationship types.
    
    Returns:
        set: Set of temporal relationship type string values
    """
    return {
        StandardRelationshipType.CREATED_BEFORE.value,
        StandardRelationshipType.CREATED_AFTER.value,
        StandardRelationshipType.MODIFIED_BEFORE.value,
        StandardRelationshipType.MODIFIED_AFTER.value
    }


# Relationship type categories for easier management
RELATIONSHIP_CATEGORIES = {
    "hierarchical": get_hierarchical_relationship_types(),
    "content": get_content_relationship_types(),
    "authorship": get_authorship_relationship_types(),
    "access": get_access_relationship_types(),
    "version": get_version_relationship_types(),
    "temporal": get_temporal_relationship_types()
}


def get_relationship_category(relationship_type: str) -> str:
    """
    Get the category of a relationship type.
    
    Args:
        relationship_type: The relationship type string
        
    Returns:
        str: The category name, or "unknown" if not found
    """
    for category, types in RELATIONSHIP_CATEGORIES.items():
        if relationship_type in types:
            return category
    return "unknown"


def is_valid_relationship_type(relationship_type: str) -> bool:
    """
    Check if a relationship type is valid.
    
    Args:
        relationship_type: The relationship type string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    return relationship_type in get_all_standard_relationship_types()


def get_inverse_relationship(relationship_type: str) -> str:
    """
    Get the inverse of a relationship type if it exists.
    
    Args:
        relationship_type: The relationship type string
        
    Returns:
        str: The inverse relationship type, or the original if no inverse exists
    """
    inverse_map = {
        StandardRelationshipType.CONTAINS.value: StandardRelationshipType.BELONGS_TO.value,
        StandardRelationshipType.BELONGS_TO.value: StandardRelationshipType.CONTAINS.value,
        StandardRelationshipType.PARENT_OF.value: StandardRelationshipType.CHILD_OF.value,
        StandardRelationshipType.CHILD_OF.value: StandardRelationshipType.PARENT_OF.value,
        StandardRelationshipType.CREATED_BEFORE.value: StandardRelationshipType.CREATED_AFTER.value,
        StandardRelationshipType.CREATED_AFTER.value: StandardRelationshipType.CREATED_BEFORE.value,
        StandardRelationshipType.MODIFIED_BEFORE.value: StandardRelationshipType.MODIFIED_AFTER.value,
        StandardRelationshipType.MODIFIED_AFTER.value: StandardRelationshipType.MODIFIED_BEFORE.value,
        StandardRelationshipType.PREVIOUS_VERSION.value: StandardRelationshipType.NEXT_VERSION.value,
        StandardRelationshipType.NEXT_VERSION.value: StandardRelationshipType.PREVIOUS_VERSION.value,
        StandardRelationshipType.PRECEDES.value: StandardRelationshipType.FOLLOWS.value,
        StandardRelationshipType.FOLLOWS.value: StandardRelationshipType.PRECEDES.value
    }
    
    return inverse_map.get(relationship_type, relationship_type)