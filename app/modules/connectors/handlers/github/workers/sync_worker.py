import time
import threading
import structlog
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from app.utils.redis.redis_service import RedisService
from app.modules.connectors.handlers.github.services.github_service import GitHubService

logger = structlog.get_logger()


class GitHubSyncWorker:
    """
    Background worker for GitHub synchronization operations.
    """

    def __init__(self):
        self.redis_service = RedisService()
        self.github_service = GitHubService()
        self.running = False
        self.worker_thread = None
        self.sync_interval = 3600  # 1 hour default
        
    def start(self):
        """Start the sync worker."""
        if self.running:
            logger.warning("GitHub sync worker is already running")
            return
            
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("GitHub sync worker started")
        
    def stop(self):
        """Stop the sync worker."""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=10)
        logger.info("GitHub sync worker stopped")
        
    def _worker_loop(self):
        """Main worker loop."""
        while self.running:
            try:
                # Check for scheduled sync jobs
                self._process_sync_queue()
                
                # Sleep for a short interval before checking again
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in GitHub sync worker loop: {str(e)}")
                time.sleep(60)  # Wait longer on error
                
    def _process_sync_queue(self):
        """Process the GitHub sync queue."""
        try:
            # Get jobs from the sync queue
            current_time = datetime.now().timestamp()
            
            # Check for jobs that are ready to run
            jobs = self.redis_service.zrangebyscore(
                "github_sync_queue", 
                0, 
                current_time, 
                withscores=True
            )
            
            for job_key, scheduled_time in jobs:
                try:
                    # Get job data
                    job_data = self.redis_service.get(job_key)
                    if not job_data:
                        # Remove invalid job from queue
                        self.redis_service.zrem("github_sync_queue", job_key)
                        continue
                    
                    import json
                    job_info = json.loads(job_data)
                    
                    # Process the sync job
                    self._execute_sync_job(job_info)
                    
                    # Remove completed job from queue
                    self.redis_service.zrem("github_sync_queue", job_key)
                    self.redis_service.delete(job_key)
                    
                except Exception as e:
                    logger.error(f"Error processing sync job {job_key}: {str(e)}")
                    # Remove failed job from queue
                    self.redis_service.zrem("github_sync_queue", job_key)
                    self.redis_service.delete(job_key)
                    
        except Exception as e:
            logger.error(f"Error processing GitHub sync queue: {str(e)}")
            
    def _execute_sync_job(self, job_info: Dict[str, Any]):
        """Execute a sync job."""
        try:
            job_type = job_info.get('type')
            organisation_id = job_info.get('organisation_id')
            
            logger.info(f"Executing GitHub sync job: {job_type} for organisation {organisation_id}")
            
            if job_type == 'full_sync':
                # Perform full sync
                success, message, repos_synced, total_synced = self.github_service.sync_github(
                    organisation_id=organisation_id,
                    full_sync=True
                )
                
                if success:
                    logger.info(f"GitHub full sync completed: {repos_synced} repositories, {total_synced} total items")
                else:
                    logger.error(f"GitHub full sync failed: {message}")
                    
            elif job_type == 'incremental_sync':
                # Perform incremental sync
                success, message, repos_synced, total_synced = self.github_service.sync_github(
                    organisation_id=organisation_id,
                    full_sync=False
                )
                
                if success:
                    logger.info(f"GitHub incremental sync completed: {repos_synced} repositories, {total_synced} total items")
                else:
                    logger.error(f"GitHub incremental sync failed: {message}")
                    
            elif job_type == 'repository_sync':
                # Sync specific repository
                github_url = job_info.get('github_url')
                agent_id = job_info.get('agent_id')
                user_id = job_info.get('user_id')
                
                success, message, items_synced = self.github_service.sync_repository_by_url(
                    github_url=github_url,
                    agent_id=agent_id,
                    user_id=user_id,
                    organisation_id=organisation_id,
                    full_sync=job_info.get('full_sync', False)
                )
                
                if success:
                    logger.info(f"GitHub repository sync completed: {items_synced} items")
                else:
                    logger.error(f"GitHub repository sync failed: {message}")
                    
            else:
                logger.warning(f"Unknown GitHub sync job type: {job_type}")
                
        except Exception as e:
            logger.error(f"Error executing GitHub sync job: {str(e)}")
            
    def schedule_sync(self, 
                     organisation_id: str, 
                     sync_type: str = 'incremental_sync',
                     delay_minutes: int = 0,
                     **kwargs) -> str:
        """
        Schedule a GitHub sync job.
        
        Args:
            organisation_id: Organisation ID
            sync_type: Type of sync (full_sync, incremental_sync, repository_sync)
            delay_minutes: Delay in minutes before execution
            **kwargs: Additional job parameters
            
        Returns:
            Job ID
        """
        try:
            import json
            import uuid
            
            # Create job ID
            job_id = str(uuid.uuid4())
            job_key = f"github_sync:{job_id}:{organisation_id}:{sync_type}"
            
            # Calculate execution time
            execution_time = datetime.now() + timedelta(minutes=delay_minutes)
            
            # Create job data
            job_data = {
                'id': job_id,
                'type': sync_type,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat(),
                'scheduled_for': execution_time.isoformat(),
                **kwargs
            }
            
            # Store job data
            self.redis_service.set(job_key, json.dumps(job_data), ex=86400)  # Expire in 24 hours
            
            # Add to sync queue
            self.redis_service.zadd("github_sync_queue", {job_key: execution_time.timestamp()})
            
            logger.info(f"Scheduled GitHub sync job: {job_id} for {execution_time}")
            return job_id
            
        except Exception as e:
            logger.error(f"Error scheduling GitHub sync job: {str(e)}")
            raise
            
    def cancel_sync(self, job_id: str, organisation_id: str, sync_type: str) -> bool:
        """
        Cancel a scheduled sync job.
        
        Args:
            job_id: Job ID
            organisation_id: Organisation ID
            sync_type: Sync type
            
        Returns:
            True if cancelled successfully
        """
        try:
            job_key = f"github_sync:{job_id}:{organisation_id}:{sync_type}"
            
            # Remove from queue and delete job data
            self.redis_service.zrem("github_sync_queue", job_key)
            self.redis_service.delete(job_key)
            
            logger.info(f"Cancelled GitHub sync job: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling GitHub sync job: {str(e)}")
            return False
            
    def get_scheduled_jobs(self, organisation_id: str) -> list:
        """
        Get scheduled sync jobs for an organisation.
        
        Args:
            organisation_id: Organisation ID
            
        Returns:
            List of scheduled jobs
        """
        try:
            import json
            
            # Get all jobs from queue
            all_jobs = self.redis_service.zrange("github_sync_queue", 0, -1, withscores=True)
            
            # Filter jobs for this organisation
            org_jobs = []
            for job_key, scheduled_time in all_jobs:
                if f":{organisation_id}:" in job_key:
                    job_data = self.redis_service.get(job_key)
                    if job_data:
                        job_info = json.loads(job_data)
                        job_info['scheduled_timestamp'] = scheduled_time
                        org_jobs.append(job_info)
                        
            return org_jobs
            
        except Exception as e:
            logger.error(f"Error getting scheduled GitHub sync jobs: {str(e)}")
            return []
            
    def get_sync_status(self, organisation_id: str) -> Dict[str, Any]:
        """
        Get sync status for an organisation.
        
        Args:
            organisation_id: Organisation ID
            
        Returns:
            Sync status information
        """
        try:
            # Get sync statistics
            stats = self.github_service.get_sync_statistics(organisation_id)
            
            # Get scheduled jobs
            scheduled_jobs = self.get_scheduled_jobs(organisation_id)
            
            return {
                'organisation_id': organisation_id,
                'statistics': stats,
                'scheduled_jobs_count': len(scheduled_jobs),
                'worker_running': self.running,
                'last_check': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting GitHub sync status: {str(e)}")
            return {
                'organisation_id': organisation_id,
                'error': str(e),
                'worker_running': self.running
            }
