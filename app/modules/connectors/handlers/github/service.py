"""
GitHub Connector Service

Main service implementation for the GitHub connector following the GDrive pattern.
Provides comprehensive GitHub integration including repositories, issues, pull requests,
commits, and organizational entity integration.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Generator
import json

from app.modules.connectors.base import BaseConnector
from app.modules.connectors.handlers.github.constants.entities import EntityType, get_all_entity_types
from app.modules.connectors.handlers.github.constants.relationships import GitHubRelationshipType, get_all_relationship_types
from app.modules.connectors.handlers.github.connection import GitHubConnection
from app.modules.connectors.handlers.github.schema import (
    GitHubConnectorConfig, GitHubSyncResult, GitHubSearchRequest
)
from app.modules.connectors.utilities.constant.schemas import (
    ConnectorSearchResponse,
    SearchResultItem,
    SearchStatus,
    SearchMetrics,
    SearchError,
    ConnectorInfo
)
from app.modules.connectors.handlers.github.services.github_service import GitHubService

logger = logging.getLogger(__name__)


class GitHubConnectorService(BaseConnector):
    """
    GitHub Connector Service
    
    Main connector implementation that follows the GDrive pattern for consistency.
    Provides GitHub data integration with organizational entities.
    """
    
    # Connector metadata
    CONNECTOR_TYPE = "code_repository"
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize GitHub connector service.
        
        Args:
            config: Connector configuration dictionary
        """
        super().__init__(config)
        
        self.config = GitHubConnectorConfig(**config) if isinstance(config, dict) else config
        self.connection: Optional[GitHubConnection] = None
        self.source_type = "github"
        self.connector_name = "GitHub"
        self.version = "1.0.0"
        self._connected = False
        
        # Initialize the GitHub service
        self._github_service = GitHubService()

    def connect(self) -> Any:
        """
        Establish connection to GitHub API and validate credentials.
        
        Returns:
            GitHubConnection: Connected API client
        """
        try:
            logger.info("Connecting to GitHub API...")
            
            self.connection = GitHubConnection(self.config.dict() if hasattr(self.config, 'dict') else self.config)
            
            if self.connection.connect():
                self._connected = True
                logger.info("Successfully connected to GitHub API")
                return self.connection
            else:
                raise ConnectionError("Failed to establish GitHub API connection")
                
        except Exception as e:
            logger.error(f"GitHub connection failed: {str(e)}")
            self._connected = False
            raise

    def get_connector(self) -> dict:
        """
        Returns metadata about the GitHub connector.
        
        Returns:
            dict: Connector metadata information
        """
        return {
            "source_type": self.source_type,
            "name": self.connector_name,
            "version": self.version,
            "connector_type": self.CONNECTOR_TYPE,
            "description": "Comprehensive GitHub connector for repository, issue, and pull request synchronization, search, and content processing.",
            "supported_entities": list(get_all_entity_types()),
            "supported_relationships": list(get_all_relationship_types()),
            "capabilities": [
                "data_fetching",
                "search",
                "sync",
                "incremental_sync",
                "repository_processing",
                "entity_extraction",
                "relationship_mapping",
                "semantic_search",
                "full_text_search"
            ],
            "api_endpoints": {
                "main_api": "https://api.github.com"
            },
            "rate_limits": {
                "per_hour": self.config.get('rate_limit_per_hour', 5000) if isinstance(self.config, dict) else getattr(self.config, 'rate_limit_per_hour', 5000)
            },
            "authentication": {
                "type": "personal_access_token",
                "required_fields": ["token"]
            }
        }

    def test_connection(self) -> Dict[str, Any]:
        """
        Test the GitHub API connection.
        
        Returns:
            dict: Connection test results
        """
        try:
            if not self.connection:
                self.connect()
            
            return self.connection.get_connection_info()
            
        except Exception as e:
            logger.error(f"GitHub connection test failed: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def fetch_data(self, **kwargs) -> Generator[Dict[str, Any], None, None]:
        """
        Fetch data from GitHub and yield individual entities.
        
        Yields:
            dict: Individual GitHub entities (repositories, issues, etc.)
        """
        try:
            logger.info("Starting comprehensive GitHub data fetch...")
            
            organisation_id = self.config.get('organisation_id') if isinstance(self.config, dict) else getattr(self.config, 'organisation_id', None)
            if not organisation_id:
                raise ValueError("Organisation ID is required for data fetching")
            
            # Use the existing service to perform sync and yield data
            success, message, repositories_synced, total_items_synced = self._github_service.sync_github(
                organisation_id, self.config.get('full_sync', False) if isinstance(self.config, dict) else getattr(self.config, 'full_sync', False)
            )
            
            if not success:
                raise Exception(f"Sync failed: {message}")
            
            # For now, we'll yield a summary of the sync operation
            # In a full implementation, this would yield individual entities
            yield {
                'type': 'sync_summary',
                'repositories_synced': repositories_synced,
                'total_items_synced': total_items_synced,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during GitHub data fetch: {str(e)}")
            raise

    def search(self, query: str, **kwargs) -> ConnectorSearchResponse:
        """
        Search GitHub content using the query.
        
        Args:
            query: Search query string
            **kwargs: Additional search parameters
            
        Returns:
            ConnectorSearchResponse: Search results
        """
        try:
            logger.info(f"Searching GitHub content with query: {query}")
            
            organisation_id = self.config.get('organisation_id') if isinstance(self.config, dict) else getattr(self.config, 'organisation_id', None)
            if not organisation_id:
                raise ValueError("Organisation ID is required for search")
            
            limit = kwargs.get('limit', 10)
            
            # Use the GitHub service to search content
            results = self._github_service.search_github_content(
                organisation_id=organisation_id,
                query=query,
                limit=limit
            )
            
            # Convert results to SearchResultItem format
            search_items = []
            for result in results:
                item = SearchResultItem(
                    id=str(result.get('id', '')),
                    title=result.get('title', ''),
                    content=result.get('content', ''),
                    url=result.get('url', ''),
                    source=self.source_type,
                    entity_type=result.get('type', ''),
                    score=result.get('score', 0.0),
                    metadata=result.get('metadata', {})
                )
                search_items.append(item)
            
            return ConnectorSearchResponse(
                status=SearchStatus.SUCCESS,
                results=search_items,
                total_count=len(search_items),
                metrics=SearchMetrics(
                    total_results=len(search_items),
                    search_time_ms=0,  # Would be calculated in real implementation
                    sources_searched=[self.source_type]
                ),
                query=query,
                connector_type=self.CONNECTOR_TYPE
            )
            
        except Exception as e:
            logger.error(f"Error during GitHub search: {str(e)}")
            return ConnectorSearchResponse(
                status=SearchStatus.ERROR,
                results=[],
                total_count=0,
                error=SearchError(
                    code="SEARCH_FAILED",
                    message=str(e)
                ),
                query=query,
                connector_type=self.CONNECTOR_TYPE
            )

    def sync(self):
        """
        Perform a full sync of the GitHub data source.
        """
        try:
            logger.info("Starting full GitHub sync...")
            
            organisation_id = self.config.get('organisation_id') if isinstance(self.config, dict) else getattr(self.config, 'organisation_id', None)
            if not organisation_id:
                raise ValueError("Organisation ID is required for sync")
            
            # Use the existing service to perform sync
            success, message, repositories_synced, total_items_synced = self._github_service.sync_github(
                organisation_id, full_sync=True
            )
            
            if not success:
                raise Exception(f"Sync failed: {message}")
            
            logger.info(f"Completed full GitHub sync: {repositories_synced} repositories, {total_items_synced} total items")
            
        except Exception as e:
            logger.error(f"Error during GitHub full sync: {str(e)}")
            raise

    def sync_by_url(self, url: str, agent_id: str, user_id: Optional[str] = None, full_sync: bool = False) -> Dict[str, Any]:
        """
        Sync a specific GitHub repository by URL.
        
        Args:
            url: GitHub repository URL
            agent_id: Agent ID performing the sync
            user_id: User ID (optional)
            full_sync: Whether to perform full sync
            
        Returns:
            dict: Sync results
        """
        try:
            logger.info(f"Starting GitHub repository sync by URL: {url}")
            
            organisation_id = self.config.get('organisation_id') if isinstance(self.config, dict) else getattr(self.config, 'organisation_id', None)
            if not organisation_id:
                raise ValueError("Organisation ID is required for sync")
            
            # Use the GitHub service to sync by URL
            success, message, items_synced = self._github_service.sync_repository_by_url(
                github_url=url,
                agent_id=agent_id,
                user_id=user_id,
                organisation_id=organisation_id,
                full_sync=full_sync
            )
            
            return {
                'success': success,
                'message': message,
                'items_synced': items_synced,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during GitHub URL sync: {str(e)}")
            return {
                'success': False,
                'message': f"Sync failed: {str(e)}",
                'items_synced': 0,
                'timestamp': datetime.now().isoformat()
            }

    def get_sync_status(self) -> Dict[str, Any]:
        """
        Get the current sync status and statistics.
        
        Returns:
            dict: Sync status information
        """
        try:
            organisation_id = self.config.get('organisation_id') if isinstance(self.config, dict) else getattr(self.config, 'organisation_id', None)
            if not organisation_id:
                return {"error": "Organisation ID is required"}
            
            # Get sync statistics from the GitHub service
            stats = self._github_service.get_sync_statistics(organisation_id)
            
            return {
                'organisation_id': organisation_id,
                'connector_type': self.CONNECTOR_TYPE,
                'statistics': stats,
                'last_check': datetime.now().isoformat(),
                'connected': self._connected
            }
            
        except Exception as e:
            logger.error(f"Error getting GitHub sync status: {str(e)}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def disconnect(self):
        """
        Disconnect from GitHub API and cleanup resources.
        """
        try:
            if self.connection:
                self.connection.disconnect()
                self.connection = None
            
            self._connected = False
            logger.info("Disconnected from GitHub API")
            
        except Exception as e:
            logger.error(f"Error during GitHub disconnect: {str(e)}")

    def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check of the connector.
        
        Returns:
            dict: Health check results
        """
        try:
            if not self._connected:
                return {
                    'status': 'disconnected',
                    'message': 'Not connected to GitHub API',
                    'timestamp': datetime.now().isoformat()
                }
            
            # Test connection
            connection_info = self.test_connection()
            
            return {
                'status': 'healthy' if connection_info.get('status') == 'connected' else 'unhealthy',
                'connection_info': connection_info,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during GitHub health check: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
