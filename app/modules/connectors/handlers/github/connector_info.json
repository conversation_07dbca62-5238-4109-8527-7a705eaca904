{"source_type": "github", "name": "GitHub", "connector_type": "code_repository", "category": "Version Control", "icon": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40NzcgMiAyIDYuNDc3IDIgMTJDMiAxNi45OTEgNS42NTcgMjEuMTI4IDEwLjQzOCAyMS44NzhDMTEuMDM4IDIxLjk4OCAxMS4yNjMgMjEuNjI4IDExLjI2MyAyMS4zMThDMTEuMjYzIDIxLjAzOCAxMS4yNTMgMjAuMjE4IDExLjI0OCAxOS4xNThDNy44NDMgMTkuODc4IDcuMTQzIDE3Ljc0OCA3LjE0MyAxNy43NDhDNi42MzMgMTYuNDI4IDUuODkzIDE2LjA2OCA1Ljg5MyAxNi4wNjhDNC44NDMgMTUuMzE4IDUuOTczIDE1LjMzOCA1Ljk3MyAxNS4zMzhDNy4xMTMgMTUuNDI4IDcuNzMzIDE2LjU2OCA3LjczMyAxNi41NjhDOC43MjMgMTguMjc4IDEwLjM0MyAxNy43NzggMTEuMjgzIDE3LjQ4OEMxMS4zODMgMTYuNzQ4IDExLjY5MyAxNi4yNDggMTIuMDMzIDE1Ljk2OEM5LjQzMyAxNS42NzggNi42MjMgMTQuNzE4IDYuNjIzIDEwLjc4OEM2LjYyMyA5LjUyOCA3LjA3MyA4LjQ5OCA3Ljc1MyA3LjY5OEM3LjYzMyA3LjQwOCA3LjIwMyA2LjI2OCA3Ljg3MyA0LjY0OEM3Ljg3MyA0LjY0OCA4LjkxMyA0LjMxOCAxMS4yNDMgNS43NThDMTIuMTQzIDUuNTA4IDEzLjA5MyA1LjM4OCAxNC4wNDMgNS4zODhDMTQuOTkzIDUuMzg4IDE1Ljk0MyA1LjUwOCAxNi44NDMgNS43NThDMTkuMTczIDQuMzE4IDIwLjIxMyA0LjY0OCAyMC4yMTMgNC42NDhDMjAuODgzIDYuMjY4IDIwLjQ1MyA3LjQwOCAyMC4zMzMgNy42OThDMjEuMDEzIDguNDk4IDIxLjQ2MyA5LjUyOCAyMS40NjMgMTAuNzg4QzIxLjQ2MyAxNC43MjggMTguNjUzIDE1LjY3OCAxNi4wNTMgMTUuOTY4QzE2LjQ1MyAxNi4zMDggMTYuODEzIDE2Ljk3OCAxNi44MTMgMTguMDM4QzE2LjgxMyAxOS41NzggMTYuNzk3IDIwLjgxOCAxNi43OTcgMjEuMzE4QzE2Ljc5NyAyMS42MjggMTcuMDIzIDIxLjk4OCAxNy42MjMgMjEuODc4QzIyLjQwMyAyMS4xMjggMjYuMDYgMTYuOTkxIDI2LjA2IDEyQzI2LjA2IDYuNDc3IDIxLjU4MyAyIDEyIDJaIiBmaWxsPSIjMjQyOTJFIi8+Cjwvc3ZnPgo=", "description": "Comprehensive GitHub connector that provides seamless integration with GitHub for repository, issue, and pull request synchronization, content processing, and intelligent search capabilities. Supports real-time sync, permission management, and advanced code analysis.", "purpose": "This connector enables organizations to integrate their GitHub data into the knowledge graph, providing unified search across repositories, automatic content processing, and intelligent relationship mapping between code, issues, pull requests, and users.", "nodes": ["GitHubRepository", "GitHubUser", "GitHubIssue", "GitHubPullRequest", "GitHubCommit", "GitHubCodeFile", "GitHubOrganization", "GitHubBranch", "GitHubTag", "GitHubRelease", "GitHubComment", "GitHubReview", "GitHubTeam", "GitHubDirectory", "GitHubTextChunk"], "relationships": ["OWNS_REPOSITORY", "CONTRIBUTES_TO", "CREATES_ISSUE", "CREATES_PULL_REQUEST", "AUTHORS_COMMIT", "BELONGS_TO_REPO", "CONTAINS_FILE", "MODIFIES_FILE", "HAS_ACCESS", "ASSIGNED_TO_ISSUE", "ASSIGNED_TO_PR", "REVIEWS_PULL_REQUEST", "MERGES_PULL_REQUEST", "CLOSES_ISSUE", "FIXES_ISSUE", "FORKS_FROM", "STARS", "WATCHES"], "version": "1.0.0", "authentication": {"type": "personal_access_token", "required_fields": ["token"], "optional_fields": ["base_url"], "documentation": "https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token"}, "rate_limits": {"requests_per_hour": "5000", "burst_limit": "100 per minute", "quota_management": "Automatic retry with exponential backoff"}, "data_freshness": {"real_time": "Supports real-time updates through periodic sync and webhook integration", "batch_sync": "Configurable full synchronization intervals (daily, weekly, or on-demand)", "incremental": "Efficient delta updates based on repository modification timestamps and commit history"}, "compliance": {"data_privacy": "Respects GitHub privacy settings and organizational access controls. Only processes repositories accessible to the configured token.", "security": "Uses secure personal access token authentication with encrypted credential storage", "audit": "Maintains comprehensive audit logs of all data access, modifications, and sync operations"}, "supported_file_types": ["Source code files (Python, JavaScript, Java, C++, etc.)", "Markdown files", "Documentation files", "Configuration files (JSON, YAML, XML, etc.)", "Text files", "README files", "License files", "Jupyter Notebooks", "Docker files", "CI/CD configuration files"], "capabilities": ["repositories", "issues", "pull_requests", "commits", "code_search", "organizations", "users", "branches", "tags", "releases", "webhooks", "teams", "permissions"], "data_types": ["repository", "issue", "pull_request", "commit", "file", "user", "organization", "branch", "tag", "release", "comment", "review"], "integration_features": {"semantic_search": "Advanced semantic search across code, issues, and documentation", "code_analysis": "Automatic code structure analysis and relationship mapping", "collaboration_tracking": "Track collaboration patterns and contributor relationships", "issue_linking": "Intelligent linking between issues, pull requests, and commits", "dependency_mapping": "Map dependencies and relationships between repositories", "activity_monitoring": "Monitor and analyze development activity patterns"}, "sync_options": {"full_sync": "Complete synchronization of all accessible repositories and their data", "incremental_sync": "Sync only changed items since last synchronization", "selective_sync": "Sync specific repositories or organizations", "real_time_updates": "Near real-time updates through webhook integration"}}