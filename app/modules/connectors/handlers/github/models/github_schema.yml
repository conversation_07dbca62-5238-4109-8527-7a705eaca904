# GitHub Connector Schema
version: 1.1
description: "Comprehensive schema for GitHub connector entities and relationships, aligned with constants."

nodes:
  GitHubOrganization:
    description: "Represents a GitHub organization"
    properties:
      id:
        type: string
        required: true
        description: "GitHub organization ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      login:
        type: string
        description: "Organization login name"
      name:
        type: string
        description: "Organization display name"
      description:
        type: string
        description: "Organization description"
      html_url:
        type: string
        description: "Organization HTML URL"

  GitHubRepository:
    description: "Represents a GitHub repository"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub repository ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      name:
        type: string
        description: "Repository name"
      full_name:
        type: string
        description: "Repository full name (owner/repo)"
      description:
        type: string
        description: "Repository description"
      private:
        type: boolean
        description: "Whether the repository is private"
      html_url:
        type: string
        description: "Repository HTML URL"

  GitHubUser:
    description: "Represents a GitHub user"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub user ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      login:
        type: string
        description: "User login name"
      name:
        type: string
        description: "User display name"
      email:
        type: string
        description: "User email address"
      html_url:
        type: string
        description: "User profile HTML URL"

  GitHubIssue:
    description: "Represents a GitHub issue"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub issue ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      number:
        type: integer
        description: "Issue number within the repository"
      title:
        type: string
        description: "Issue title"
      state:
        type: string
        description: "Issue state (open, closed)"

  GitHubPullRequest:
    description: "Represents a GitHub pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub pull request ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      number:
        type: integer
        description: "Pull request number within the repository"
      title:
        type: string
        description: "Pull request title"
      state:
        type: string
        description: "Pull request state (open, closed, merged)"

  GitHubCommit:
    description: "Represents a GitHub commit"
    properties:
      sha:
        type: string
        required: true
        description: "Commit SHA hash"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      message:
        type: string
        description: "Commit message"
      author_email:
        type: string
        description: "Email of the commit author"

  GitHubCodeFile:
    description: "Represents a code file in GitHub"
    properties:
      path:
        type: string
        required: true
        description: "File path within the repository"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this file"
      sha:
        type: string
        description: "File content SHA hash"
      size:
        type: integer
        description: "File size in bytes"

  GitHubBranch:
    description: "Represents a repository branch"
    properties:
      name:
        type: string
        required: true
        description: "Branch name"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this branch"
      last_commit_sha:
        type: string
        description: "SHA of the last commit on this branch"

  GitHubDirectory:
    description: "Represents a directory in a GitHub repository"
    properties:
      path:
        type: string
        required: true
        description: "Directory path within the repository"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this directory"

  GitHubTeam:
    description: "Represents a GitHub team"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub team ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      name:
        type: string
        description: "Team name"

  GitHubTag:
    description: "Represents a Git tag"
    properties:
      name:
        type: string
        required: true
        description: "Tag name"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this tag"

  GitHubRelease:
    description: "Represents a GitHub release"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub release ID"
      repository_id:
        type: integer
        required: true
        description: "ID of the repository containing this release"
      tag_name:
        type: string
        description: "Tag name associated with this release"

  GitHubComment:
    description: "Represents a comment on an issue or pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub comment ID"
      body:
        type: string
        description: "Comment body text"

  GitHubReview:
    description: "Represents a review on a pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub review ID"
      state:
        type: string
        description: "Review state (approved, changes_requested, commented)"

relationships:
  OWNS_REPOSITORY:
    from: GitHubUser
    to: GitHubRepository
    description: "User owns a repository"
    direction: "->"
    properties:
      ownership_type:
        type: string
        description: "Type of ownership (owner, admin)"
      granted_at:
        type: timestamp
        description: "When ownership was granted"

  CONTRIBUTES_TO:
    from: GitHubUser
    to: GitHubRepository
    description: "User contributes to a repository"
    direction: "->"
    properties:
      first_contribution_at:
        type: timestamp
        description: "When user first contributed"
      last_contribution_at:
        type: timestamp
        description: "When user last contributed"

  FORKS_FROM:
    from: GitHubRepository
    to: GitHubRepository
    description: "A repository is forked from another"
    direction: "->"
    properties:
      forked_at:
        type: timestamp
        description: "When the fork was created"

  STARS:
    from: GitHubUser
    to: GitHubRepository
    description: "User stars a repository"
    direction: "->"
    properties:
      starred_at:
        type: timestamp
        description: "When the repository was starred"

  WATCHES:
    from: GitHubUser
    to: GitHubRepository
    description: "User watches a repository"
    direction: "->"
    properties:
      watched_at:
        type: timestamp
        description: "When the repository was watched"

  ORG_OWNS_REPO:
    from: GitHubOrganization
    to: GitHubRepository
    description: "Organization owns a repository"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the repository was created"

  CREATES_ISSUE:
    from: GitHubUser
    to: GitHubIssue
    description: "User creates an issue"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the issue was created"

  ASSIGNED_TO_ISSUE:
    from: GitHubUser
    to: GitHubIssue
    description: "User is assigned to an issue"
    direction: "->"
    properties:
      assigned_at:
        type: timestamp
        description: "When the user was assigned to the issue"

  CREATES_PULL_REQUEST:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User creates a pull request"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the pull request was created"

  REVIEWS_PULL_REQUEST:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User reviews a pull request"
    direction: "->"
    properties:
      reviewed_at:
        type: timestamp
        description: "When the pull request was reviewed"

  MERGES_PULL_REQUEST:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User merges a pull request"
    direction: "->"
    properties:
      merged_at:
        type: timestamp
        description: "When the pull request was merged"

  AUTHORS_COMMIT:
    from: GitHubUser
    to: GitHubCommit
    description: "User authors a commit"
    direction: "->"
    properties:
      authored_at:
        type: timestamp
        description: "When the commit was authored"

  COMMITS_TO_BRANCH:
    from: GitHubCommit
    to: GitHubBranch
    description: "A commit is made to a branch"
    direction: "->"
    properties:
      committed_at:
        type: timestamp
        description: "When the commit was made to the branch"

  MODIFIES_FILE:
    from: GitHubCommit
    to: GitHubCodeFile
    description: "A commit modifies a file"
    direction: "->"
    properties:
      modification_type:
        type: string
        description: "Type of modification (added, modified, deleted)"
      lines_added:
        type: integer
        description: "Number of lines added"
      lines_removed:
        type: integer
        description: "Number of lines removed"

  CONTAINS_FILE:
    from: GitHubDirectory
    to: GitHubCodeFile
    description: "A directory contains a file"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When the file was added to the directory"

  CONTAINS_DIRECTORY:
    from: GitHubDirectory
    to: GitHubDirectory
    description: "A directory contains another directory"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When the subdirectory was added"

  MEMBER_OF_ORG:
    from: GitHubUser
    to: GitHubOrganization
    description: "User is a member of an organization"
    direction: "->"
    properties:
      role:
        type: string
        description: "User's role in the organization (member, admin, owner)"
      joined_at:
        type: timestamp
        description: "When the user joined the organization"

  MEMBER_OF_TEAM:
    from: GitHubUser
    to: GitHubTeam
    description: "User is a member of a team"
    direction: "->"
    properties:
      role:
        type: string
        description: "User's role in the team (member, maintainer)"
      joined_at:
        type: timestamp
        description: "When the user joined the team"

  TEAM_HAS_ACCESS:
    from: GitHubTeam
    to: GitHubRepository
    description: "A team has access to a repository"
    direction: "->"
    properties:
      permission:
        type: string
        description: "Permission level (read, write, admin)"
      granted_at:
        type: timestamp
        description: "When access was granted"

  COMMENTS_ON:
    from: GitHubUser
    to: [GitHubIssue, GitHubPullRequest]
    description: "User comments on an issue or pull request"
    direction: "->"
    properties:
      commented_at:
        type: timestamp
        description: "When the comment was made"

  BELONGS_TO:
    from: [GitHubIssue, GitHubPullRequest, GitHubCommit]
    to: GitHubRepository
    description: "An entity belongs to a repository"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the entity was created in the repository"