import os
import structlog
from app.modules.connectors.utilities.schema_loader import ConnectorSchema, ConnectorSchemaError

logger = structlog.get_logger()

# Pre-initialized schemas for GitHub connector
try:
    schema_path = os.path.join(os.path.dirname(__file__), "github_schema.yml")
    github_schema = ConnectorSchema("github", schema_path)
except ConnectorSchemaError as e:
    logger.warning(f"Could not load GitHub schema: {e}")
    github_schema = None
