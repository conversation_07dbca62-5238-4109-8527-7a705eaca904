import structlog
from app.services.neo4j_service import execute_write_query

logger = structlog.get_logger()

def create_github_constraints():
    """
    Create Neo4j constraints for GitHub entities.
    This is a duplicate of the services/neo4j_setup.py for compatibility.
    """
    from app.modules.connectors.handlers.github.services.neo4j_setup import create_github_constraints as create_constraints
    create_constraints()

def create_github_indexes():
    """
    Create Neo4j indexes for GitHub entities.
    This is a duplicate of the services/neo4j_setup.py for compatibility.
    """
    from app.modules.connectors.handlers.github.services.neo4j_setup import create_github_indexes as create_indexes
    create_indexes()
