"""
GitHub Connection Management

This module handles connection establishment and management for GitHub API.
"""

import logging
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json
import base64

from app.utils.source_credentials import get_source_credentials

logger = logging.getLogger(__name__)


class GitHubConnection:
    """
    Manages connection to GitHub API.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize connection with configuration.
        
        Args:
            config: Connection configuration dictionary
        """
        self.config = config
        self.session = None
        self._authenticated = False
        
        # Extract connection parameters
        self.organisation_id = config.get('organisation_id')
        self.base_url = config.get('base_url', 'https://api.github.com').rstrip('/')
        self.timeout = config.get('timeout_seconds', 30)
        
        # Rate limiting
        self.rate_limit_remaining = 5000
        self.rate_limit_reset = datetime.now()
        self.rate_limit_used = 0
        
        # Token will be retrieved from credentials
        self.token = None
    
    def connect(self) -> bool:
        """
        Establish connection and authenticate with GitHub API.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to GitHub API for organisation {self.organisation_id}")
            
            if not self.organisation_id:
                logger.error("Organisation ID is required for GitHub connection")
                return False
            
            # Get GitHub credentials from source credentials
            credentials = get_source_credentials(self.organisation_id, 'github')
            if not credentials:
                logger.error(f"No GitHub credentials found for organisation {self.organisation_id}")
                return False
            
            # Extract token from credentials
            self.token = credentials.get('token') or credentials.get('access_token')
            if not self.token:
                logger.error("GitHub token not found in credentials")
                return False
            
            # Create requests session with headers
            self.session = requests.Session()
            self.session.headers.update({
                'Authorization': f'token {self.token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0',
                'X-GitHub-Api-Version': '2022-11-28'
            })
            
            # Test connection with a simple API call
            self._test_connection()
            
            self._authenticated = True
            logger.info("Successfully connected to GitHub API")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to GitHub API: {str(e)}")
            self._authenticated = False
            return False
    
    def disconnect(self):
        """Clean up connection resources."""
        try:
            if self.session:
                self.session.close()
                self.session = None
            
            self._authenticated = False
            self.token = None
            logger.info("Disconnected from GitHub API")
            
        except Exception as e:
            logger.warning(f"Error during GitHub disconnect: {str(e)}")
    
    def is_connected(self) -> bool:
        """
        Check if connection is active and authenticated.
        
        Returns:
            bool: True if connected and authenticated
        """
        return self._authenticated and self.session is not None and self.token is not None
    
    def get_session(self):
        """
        Get the requests session instance.
        
        Returns:
            requests.Session: Session instance
            
        Raises:
            ConnectionError: If not connected
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to GitHub API. Call connect() first.")
        
        return self.session
    
    def _test_connection(self):
        """Test the connection by making a simple API call."""
        try:
            # Test with a simple user info call to verify credentials
            response = self.session.get(f"{self.base_url}/user", timeout=self.timeout)
            
            if response.status_code == 401:
                raise ConnectionError("Authentication failed - invalid token")
            elif response.status_code == 403:
                raise ConnectionError("Access denied - insufficient permissions or rate limited")
            elif response.status_code >= 400:
                raise ConnectionError(f"API test failed: HTTP {response.status_code}")
            
            user_info = response.json()
            logger.debug(f"Connection test successful. GitHub user: {user_info.get('login', 'Unknown')}")
            
            # Update rate limit info from headers
            self._update_rate_limit_from_headers(response.headers)
            
        except requests.exceptions.RequestException as e:
            raise ConnectionError(f"Connection test failed: {str(e)}")
        except Exception as e:
            raise ConnectionError(f"Connection test failed: {str(e)}")
    
    def _update_rate_limit_from_headers(self, headers: Dict[str, str]):
        """Update rate limit information from response headers."""
        try:
            if 'X-RateLimit-Remaining' in headers:
                self.rate_limit_remaining = int(headers['X-RateLimit-Remaining'])
            if 'X-RateLimit-Reset' in headers:
                self.rate_limit_reset = datetime.fromtimestamp(int(headers['X-RateLimit-Reset']))
            if 'X-RateLimit-Used' in headers:
                self.rate_limit_used = int(headers['X-RateLimit-Used'])
                
            logger.debug(f"Rate limit - Remaining: {self.rate_limit_remaining}, "
                        f"Reset: {self.rate_limit_reset}")
                        
        except (ValueError, KeyError) as e:
            logger.debug(f"Could not parse rate limit headers: {str(e)}")
    
    def _check_rate_limit(self):
        """Check if we're approaching rate limits and wait if necessary."""
        if self.rate_limit_remaining <= 10:
            if datetime.now() < self.rate_limit_reset:
                wait_time = (self.rate_limit_reset - datetime.now()).total_seconds()
                logger.warning(f"Rate limit low ({self.rate_limit_remaining}), "
                             f"waiting {wait_time:.1f} seconds")
                import time
                time.sleep(wait_time + 1)
                # Try to update rate limit info
                try:
                    response = self.session.get(f"{self.base_url}/rate_limit", timeout=self.timeout)
                    if response.status_code == 200:
                        self._update_rate_limit_from_headers(response.headers)
                except Exception as e:
                    logger.debug(f"Could not update rate limit after wait: {str(e)}")
    
    def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        Make HTTP request to GitHub API.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Dict[str, Any]: Response data
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to GitHub API. Call connect() first.")
        
        # Check rate limits before making request
        self._check_rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(method, url, timeout=self.timeout, **kwargs)
            
            # Update rate limit info from response headers
            self._update_rate_limit_from_headers(response.headers)
            
            # Handle different response types
            if response.status_code == 204:  # No content
                return {}
            
            if response.status_code == 404:
                raise ConnectionError("Resource not found")
            
            if response.status_code == 401:
                raise ConnectionError("Authentication failed - invalid token")
            
            if response.status_code == 403:
                if self.rate_limit_remaining == 0:
                    raise ConnectionError(f"Rate limit exceeded. Resets at {self.rate_limit_reset}")
                else:
                    raise ConnectionError("Access denied - insufficient permissions")
            
            if response.status_code >= 400:
                try:
                    error_data = response.json()
                    message = error_data.get('message', f'HTTP {response.status_code}')
                except:
                    message = f'HTTP {response.status_code}'
                
                raise ConnectionError(f"GitHub API error: {message}")
            
            # Parse JSON response
            if 'application/json' in response.headers.get('content-type', ''):
                return response.json()
            else:
                # Handle non-JSON responses (like raw file content)
                return {'content': response.text, 'content_type': response.headers.get('content-type')}
                
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request error for {method} {url}: {str(e)}")
            raise ConnectionError(f"Network error: {str(e)}")
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make GET request."""
        return self.make_request('GET', endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make POST request."""
        return self.make_request('POST', endpoint, json=data)
    
    def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make PUT request."""
        return self.make_request('PUT', endpoint, json=data)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request."""
        return self.make_request('DELETE', endpoint)
    
    def get_paginated(self, endpoint: str, params: Optional[Dict[str, Any]] = None, 
                     max_pages: int = 100) -> List[Dict[str, Any]]:
        """Get all pages of a paginated endpoint."""
        all_items = []
        page = 1
        per_page = params.get('per_page', 100) if params else 100
        
        # Ensure per_page is within GitHub's limits
        per_page = min(per_page, 100)
        
        while page <= max_pages:
            page_params = (params or {}).copy()
            page_params.update({'page': page, 'per_page': per_page})
            
            try:
                response = self.get(endpoint, page_params)
                
                # Handle different response formats
                if isinstance(response, list):
                    items = response
                elif isinstance(response, dict) and 'items' in response:
                    items = response['items']
                else:
                    # Single item response
                    items = [response] if response else []
                
                if not items:
                    break
                
                all_items.extend(items)
                
                # If we got fewer items than requested, we've reached the end
                if len(items) < per_page:
                    break
                
                page += 1
                
                # Small delay to be respectful to the API
                import time
                time.sleep(0.1)
                
            except ConnectionError as e:
                if "Resource not found" in str(e):
                    # No more pages
                    break
                raise
        
        return all_items
    
    def validate_permissions(self) -> Dict[str, bool]:
        """
        Validate that the token has required permissions.
        
        Returns:
            Dict[str, bool]: Dictionary of permission checks
        """
        permissions = {
            'can_read_user': False,
            'can_read_repos': False,
            'can_read_issues': False,
            'can_read_code': False
        }
        
        if not self.is_connected():
            return permissions
        
        try:
            # Test reading user info
            self.get('/user')
            permissions['can_read_user'] = True
            
            # Test reading repositories
            self.get('/user/repos', {'per_page': 1})
            permissions['can_read_repos'] = True
            
            # Test reading issues (if we have repos)
            try:
                repos = self.get('/user/repos', {'per_page': 1})
                if repos:
                    repo_name = repos[0]['full_name']
                    self.get(f'/repos/{repo_name}/issues', {'per_page': 1})
                    permissions['can_read_issues'] = True
            except:
                pass
            
            # Test reading code content
            try:
                repos = self.get('/user/repos', {'per_page': 1})
                if repos:
                    repo_name = repos[0]['full_name']
                    self.get(f'/repos/{repo_name}/contents')
                    permissions['can_read_code'] = True
            except:
                pass
                
        except Exception as e:
            logger.warning(f"Permission validation failed: {str(e)}")
        
        return permissions
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        Get information about the current connection.
        
        Returns:
            Dict[str, Any]: Connection information
        """
        info = {
            'connected': self.is_connected(),
            'organisation_id': self.organisation_id,
            'base_url': self.base_url,
            'timeout': self.timeout,
            'rate_limit_remaining': self.rate_limit_remaining,
            'rate_limit_reset': self.rate_limit_reset.isoformat() if self.rate_limit_reset else None
        }
        
        if self.is_connected():
            try:
                user_info = self.get('/user')
                rate_limit_info = self.get('/rate_limit')
                
                info.update({
                    'authenticated_user': user_info.get('login'),
                    'user_type': user_info.get('type'),
                    'user_id': user_info.get('id'),
                    'rate_limit_info': rate_limit_info.get('resources', {}),
                    'permissions': self.validate_permissions()
                })
            except Exception as e:
                logger.warning(f"Could not get connection details: {str(e)}")
        
        return info
    
    def refresh_connection(self) -> bool:
        """
        Refresh the connection (useful for long-running processes).
        
        Returns:
            bool: True if refresh successful, False otherwise
        """
        try:
            if self.is_connected():
                # Test current connection
                self._test_connection()
                return True
            else:
                # Reconnect if not connected
                return self.connect()
                
        except Exception as e:
            logger.warning(f"Connection refresh failed, attempting reconnect: {str(e)}")
            return self.connect()
    
    def get_file_content(self, repo_full_name: str, file_path: str, 
                        ref: str = 'main') -> Optional[str]:
        """Get file content from repository."""
        try:
            endpoint = f'/repos/{repo_full_name}/contents/{file_path}'
            params = {'ref': ref}
            
            response = self.get(endpoint, params)
            
            if response.get('type') == 'file' and 'content' in response:
                # Decode base64 content
                content = base64.b64decode(response['content']).decode('utf-8')
                return content
            
            return None
            
        except ConnectionError as e:
            if "Resource not found" in str(e):
                return None
            raise
    
    def search(self, search_type: str, query: str, sort: Optional[str] = None,
              order: str = 'desc', per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Search GitHub using the search API."""
        valid_search_types = ['repositories', 'code', 'commits', 'issues', 'users', 'topics']
        
        if search_type not in valid_search_types:
            raise ValueError(f"Invalid search type. Must be one of: {valid_search_types}")
        
        endpoint = f'/search/{search_type}'
        params = {
            'q': query,
            'per_page': min(per_page, 100),
            'page': page,
            'order': order
        }
        
        if sort:
            params['sort'] = sort
        
        return self.get(endpoint, params)
    
    def get_user_repositories(self, username: Optional[str] = None, 
                             repo_type: str = 'all') -> List[Dict[str, Any]]:
        """Get repositories for a user or the authenticated user."""
        if username:
            endpoint = f'/users/{username}/repos'
        else:
            endpoint = '/user/repos'
        
        params = {
            'type': repo_type,  # all, owner, public, private, member
            'sort': 'updated',
            'direction': 'desc',
            'per_page': 100
        }
        
        return self.get_paginated(endpoint, params)
    
    def get_organization_repositories(self, org_name: str) -> List[Dict[str, Any]]:
        """Get repositories for an organization."""
        endpoint = f'/orgs/{org_name}/repos'
        params = {
            'type': 'all',
            'sort': 'updated',
            'direction': 'desc',
            'per_page': 100
        }
        
        return self.get_paginated(endpoint, params)