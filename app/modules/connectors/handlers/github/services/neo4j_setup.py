import structlog
from app.services.neo4j_service import execute_write_query

logger = structlog.get_logger()

def create_github_constraints():
    """
    Create Neo4j constraints for GitHub entities.
    """
    constraints = [
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (r:GitHubRepository)
        REQUIRE r.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (u:GitHubU<PERSON>)
        REQUIRE u.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (i:GitHubIssue)
        REQUIRE i.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (pr:GitH<PERSON>PullRequest)
        REQUIRE pr.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (c:GitHubCommit)
        REQUIRE c.sha IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (f:GitHub<PERSON>odeFile)
        REQUIRE (f.path, f.organisation_id) IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (o:GitHubOrganization)
        REQUIRE o.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (b:GitHubBranch)
        REQUIRE (b.name, b.repository_id) IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (t:GitHubTag)
        REQUIRE (t.name, t.repository_id) IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (rel:GitHubRelease)
        REQUIRE rel.id IS UNIQUE
        """
    ]
    
    for query in constraints:
        try:
            execute_write_query(query)
        except Exception as e:
            logger.warning(f"Failed to create constraint: {str(e)}")
    
    logger.info("GitHub Neo4j constraints created")

def create_github_indexes():
    """
    Create Neo4j indexes for GitHub entities.
    """
    indexes = [
        """
        CREATE INDEX IF NOT EXISTS FOR (r:GitHubRepository) ON (r.organisation_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (r:GitHubRepository) ON (r.full_name)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (u:GitHubUser) ON (u.organisation_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (u:GitHubUser) ON (u.login)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (i:GitHubIssue) ON (i.organisation_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (i:GitHubIssue) ON (i.number)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (pr:GitHubPullRequest) ON (pr.organisation_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (pr:GitHubPullRequest) ON (pr.number)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (c:GitHubCommit) ON (c.organisation_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (f:GitHubCodeFile) ON (f.organisation_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (f:GitHubCodeFile) ON (f.name)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (f:GitHubCodeFile) ON (f.vector_id)
        """
    ]
    
    for query in indexes:
        try:
            execute_write_query(query)
        except Exception as e:
            logger.warning(f"Failed to create index: {str(e)}")
    
    logger.info("GitHub Neo4j indexes created")
