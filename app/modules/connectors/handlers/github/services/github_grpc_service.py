import grpc
import structlog
from typing import Dict, Any, List
from datetime import datetime

from app.grpc_ import connector_pb2, connector_pb2_grpc
from app.modules.connectors.handlers.github.services.github_service import GitHubService

logger = structlog.get_logger()


class GitHubGrpcService(connector_pb2_grpc.ConnectorServiceServicer):
    """
    gRPC service for GitHub connector operations.
    """

    def __init__(self):
        self.github_service = GitHubService()

    def SyncConnector(self, request, context):
        """
        Sync GitHub data for an organisation.
        """
        try:
            logger.info(f"GitHub sync requested for organisation: {request.organisation_id}")
            
            # Perform GitHub sync
            success, message, repositories_synced, total_items_synced = self.github_service.sync_github(
                organisation_id=request.organisation_id,
                full_sync=request.full_sync
            )
            
            if success:
                return connector_pb2.SyncResponse(
                    success=True,
                    message=message,
                    items_synced=total_items_synced,
                    sync_time=datetime.now().isoformat()
                )
            else:
                return connector_pb2.SyncResponse(
                    success=False,
                    message=message,
                    items_synced=0,
                    sync_time=datetime.now().isoformat()
                )
                
        except Exception as e:
            logger.error(f"Error in GitHub sync gRPC call: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"GitHub sync failed: {str(e)}")
            return connector_pb2.SyncResponse(
                success=False,
                message=f"Sync failed: {str(e)}",
                items_synced=0,
                sync_time=datetime.now().isoformat()
            )

    def SyncByUrl(self, request, context):
        """
        Sync a specific GitHub repository by URL.
        """
        try:
            logger.info(f"GitHub URL sync requested: {request.url}")
            
            # Perform repository sync by URL
            success, message, items_synced = self.github_service.sync_repository_by_url(
                github_url=request.url,
                agent_id=request.agent_id,
                user_id=request.user_id if request.user_id else None,
                organisation_id=request.organisation_id,
                full_sync=request.full_sync
            )
            
            if success:
                return connector_pb2.SyncByUrlResponse(
                    success=True,
                    message=message,
                    items_synced=items_synced,
                    sync_time=datetime.now().isoformat()
                )
            else:
                return connector_pb2.SyncByUrlResponse(
                    success=False,
                    message=message,
                    items_synced=0,
                    sync_time=datetime.now().isoformat()
                )
                
        except Exception as e:
            logger.error(f"Error in GitHub URL sync gRPC call: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"GitHub URL sync failed: {str(e)}")
            return connector_pb2.SyncByUrlResponse(
                success=False,
                message=f"URL sync failed: {str(e)}",
                items_synced=0,
                sync_time=datetime.now().isoformat()
            )

    def SearchConnector(self, request, context):
        """
        Search GitHub content.
        """
        try:
            logger.info(f"GitHub search requested: {request.query}")
            
            # Perform search
            results = self.github_service.search_github_content(
                organisation_id=request.organisation_id,
                query=request.query,
                limit=request.limit if request.limit > 0 else 10
            )
            
            # Convert results to protobuf format
            search_results = []
            for result in results:
                search_result = connector_pb2.SearchResult(
                    id=str(result.get('id', '')),
                    title=result.get('title', ''),
                    content=result.get('content', ''),
                    url=result.get('url', ''),
                    type=result.get('type', ''),
                    score=result.get('score', 0.0),
                    metadata=str(result.get('metadata', {}))
                )
                search_results.append(search_result)
            
            return connector_pb2.SearchResponse(
                success=True,
                results=search_results,
                total_count=len(search_results),
                message="Search completed successfully"
            )
            
        except Exception as e:
            logger.error(f"Error in GitHub search gRPC call: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"GitHub search failed: {str(e)}")
            return connector_pb2.SearchResponse(
                success=False,
                results=[],
                total_count=0,
                message=f"Search failed: {str(e)}"
            )

    def GetConnectorInfo(self, request, context):
        """
        Get GitHub connector information.
        """
        try:
            connector_info = {
                "name": "GitHub",
                "version": "1.0.0",
                "type": "code_repository",
                "description": "GitHub connector for repositories, issues, pull requests, and code",
                "capabilities": [
                    "repositories",
                    "issues", 
                    "pull_requests",
                    "commits",
                    "code_search",
                    "organizations",
                    "users"
                ],
                "authentication": {
                    "type": "personal_access_token",
                    "required_fields": ["token"]
                }
            }
            
            return connector_pb2.ConnectorInfoResponse(
                success=True,
                info=str(connector_info),
                message="Connector info retrieved successfully"
            )
            
        except Exception as e:
            logger.error(f"Error getting GitHub connector info: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get connector info: {str(e)}")
            return connector_pb2.ConnectorInfoResponse(
                success=False,
                info="{}",
                message=f"Failed to get connector info: {str(e)}"
            )

    def GetSyncStatus(self, request, context):
        """
        Get GitHub sync status and statistics.
        """
        try:
            logger.info(f"GitHub sync status requested for organisation: {request.organisation_id}")
            
            # Get sync statistics
            stats = self.github_service.get_sync_statistics(request.organisation_id)
            
            return connector_pb2.SyncStatusResponse(
                success=True,
                last_sync_time=stats.get('last_sync_time', ''),
                items_count=stats.get('repositories_count', 0) + 
                           stats.get('issues_count', 0) + 
                           stats.get('pull_requests_count', 0) + 
                           stats.get('commits_count', 0),
                status="active",
                message="Sync status retrieved successfully",
                metadata=str(stats)
            )
            
        except Exception as e:
            logger.error(f"Error getting GitHub sync status: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get sync status: {str(e)}")
            return connector_pb2.SyncStatusResponse(
                success=False,
                last_sync_time="",
                items_count=0,
                status="error",
                message=f"Failed to get sync status: {str(e)}",
                metadata="{}"
            )

    def TestConnection(self, request, context):
        """
        Test GitHub connection.
        """
        try:
            logger.info(f"GitHub connection test requested for organisation: {request.organisation_id}")
            
            # Test connection by attempting to fetch user info
            from app.utils.source_credentials import get_source_credentials
            import requests
            
            credentials = get_source_credentials(request.organisation_id, 'github')
            if not credentials:
                return connector_pb2.TestConnectionResponse(
                    success=False,
                    message="No GitHub credentials found for organisation"
                )
            
            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return connector_pb2.TestConnectionResponse(
                    success=False,
                    message="GitHub token not found in credentials"
                )
            
            # Test API call
            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })
            
            response = session.get('https://api.github.com/user')
            response.raise_for_status()
            
            user_data = response.json()
            
            return connector_pb2.TestConnectionResponse(
                success=True,
                message=f"Connection successful. Authenticated as: {user_data.get('login', 'Unknown')}"
            )
            
        except Exception as e:
            logger.error(f"Error testing GitHub connection: {str(e)}")
            return connector_pb2.TestConnectionResponse(
                success=False,
                message=f"Connection test failed: {str(e)}"
            )
