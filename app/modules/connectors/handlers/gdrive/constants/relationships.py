"""
Google Drive Connector Relationship Type Definitions

This module defines all relationship types for the Google Drive connector.
"""

from enum import Enum


class RelationshipType(Enum):
    """
    Google Drive relationship types for knowledge graph edges.
    
    Define all possible edge types that can be created in the knowledge graph
    for Google Drive data relationships.
    """
    
    # Hierarchical relationships
    CONTAINS = "CONTAINS"
    BELONGS_TO = "BELONGS_TO"
    PARENT_OF = "PARENT_OF"
    CHILD_OF = "CHILD_OF"
    
    # Access and permission relationships
    HAS_ACCESS = "HAS_ACCESS"
    CAN_READ = "CAN_READ"
    CAN_WRITE = "CAN_WRITE"
    CAN_DELETE = "CAN_DELETE"
    CAN_SHARE = "CAN_SHARE"
    OWNS = "OWNS"
    SHARED_WITH = "SHARED_WITH"
    
    # Content relationships
    PART_OF = "PART_OF"
    EXTRACTED_FROM = "EXTRACTED_FROM"
    REFERENCES = "REFERENCES"
    LINKS_TO = "LINKS_TO"
    
    # Version and history relationships
    VERSION_OF = "VERSION_OF"
    PREVIOUS_VERSION = "PREVIOUS_VERSION"
    NEXT_VERSION = "NEXT_VERSION"
    REVISED_BY = "REVISED_BY"
    
    # User action relationships
    CREATED_BY = "CREATED_BY"
    MODIFIED_BY = "MODIFIED_BY"
    COMMENTED_ON = "COMMENTED_ON"
    REVIEWED_BY = "REVIEWED_BY"
    
    # Collaboration relationships
    COLLABORATED_ON = "COLLABORATED_ON"
    CONTRIBUTED_TO = "CONTRIBUTED_TO"
    
    # Sync and agent relationships
    SYNCED_BY = "SYNCED_BY"
    PROCESSED_BY = "PROCESSED_BY"
    INDEXED_BY = "INDEXED_BY"
    
    # Similarity relationships
    SIMILAR_TO = "SIMILAR_TO"
    DUPLICATE_OF = "DUPLICATE_OF"
    
    # Temporal relationships
    CREATED_BEFORE = "CREATED_BEFORE"
    CREATED_AFTER = "CREATED_AFTER"
    MODIFIED_BEFORE = "MODIFIED_BEFORE"
    MODIFIED_AFTER = "MODIFIED_AFTER"


def get_all_relationship_types():
    """
    Returns all Google Drive relationship types for connector registration.
    
    Returns:
        set: Set of all relationship type string values
    """
    return {r.value for r in RelationshipType}


def get_hierarchical_relationship_types():
    """
    Returns hierarchical relationship types.
    
    Returns:
        set: Set of hierarchical relationship type string values
    """
    return {
        RelationshipType.CONTAINS.value,
        RelationshipType.BELONGS_TO.value,
        RelationshipType.PARENT_OF.value,
        RelationshipType.CHILD_OF.value
    }


def get_access_relationship_types():
    """
    Returns access and permission relationship types.
    
    Returns:
        set: Set of access relationship type string values
    """
    return {
        RelationshipType.HAS_ACCESS.value,
        RelationshipType.CAN_READ.value,
        RelationshipType.CAN_WRITE.value,
        RelationshipType.CAN_DELETE.value,
        RelationshipType.CAN_SHARE.value,
        RelationshipType.OWNS.value,
        RelationshipType.SHARED_WITH.value
    }


def get_content_relationship_types():
    """
    Returns content-related relationship types.
    
    Returns:
        set: Set of content relationship type string values
    """
    return {
        RelationshipType.PART_OF.value,
        RelationshipType.EXTRACTED_FROM.value,
        RelationshipType.REFERENCES.value,
        RelationshipType.LINKS_TO.value
    }


def get_user_action_relationship_types():
    """
    Returns user action relationship types.
    
    Returns:
        set: Set of user action relationship type string values
    """
    return {
        RelationshipType.CREATED_BY.value,
        RelationshipType.MODIFIED_BY.value,
        RelationshipType.COMMENTED_ON.value,
        RelationshipType.REVIEWED_BY.value
    }


def get_version_relationship_types():
    """
    Returns version and history relationship types.
    
    Returns:
        set: Set of version relationship type string values
    """
    return {
        RelationshipType.VERSION_OF.value,
        RelationshipType.PREVIOUS_VERSION.value,
        RelationshipType.NEXT_VERSION.value,
        RelationshipType.REVISED_BY.value
    }


def get_sync_relationship_types():
    """
    Returns sync and processing relationship types.
    
    Returns:
        set: Set of sync relationship type string values
    """
    return {
        RelationshipType.SYNCED_BY.value,
        RelationshipType.PROCESSED_BY.value,
        RelationshipType.INDEXED_BY.value
    }


def is_bidirectional_relationship(relationship_type: str) -> bool:
    """
    Check if a relationship type is bidirectional.
    
    Args:
        relationship_type: The relationship type string
        
    Returns:
        bool: True if bidirectional, False otherwise
    """
    bidirectional_relationships = {
        RelationshipType.SIMILAR_TO.value,
        RelationshipType.COLLABORATED_ON.value,
        RelationshipType.LINKS_TO.value
    }
    return relationship_type in bidirectional_relationships


def get_inverse_relationship(relationship_type: str) -> str:
    """
    Get the inverse of a relationship type if it exists.
    
    Args:
        relationship_type: The relationship type string
        
    Returns:
        str: The inverse relationship type, or the original if no inverse exists
    """
    inverse_map = {
        RelationshipType.CONTAINS.value: RelationshipType.BELONGS_TO.value,
        RelationshipType.BELONGS_TO.value: RelationshipType.CONTAINS.value,
        RelationshipType.PARENT_OF.value: RelationshipType.CHILD_OF.value,
        RelationshipType.CHILD_OF.value: RelationshipType.PARENT_OF.value,
        RelationshipType.PREVIOUS_VERSION.value: RelationshipType.NEXT_VERSION.value,
        RelationshipType.NEXT_VERSION.value: RelationshipType.PREVIOUS_VERSION.value,
        RelationshipType.CREATED_BEFORE.value: RelationshipType.CREATED_AFTER.value,
        RelationshipType.CREATED_AFTER.value: RelationshipType.CREATED_BEFORE.value,
        RelationshipType.MODIFIED_BEFORE.value: RelationshipType.MODIFIED_AFTER.value,
        RelationshipType.MODIFIED_AFTER.value: RelationshipType.MODIFIED_BEFORE.value
    }
    
    return inverse_map.get(relationship_type, relationship_type)


def get_relationship_category(relationship_type: str) -> str:
    """
    Get the category of a relationship type.
    
    Args:
        relationship_type: The relationship type string
        
    Returns:
        str: The category name
    """
    if relationship_type in get_hierarchical_relationship_types():
        return "hierarchical"
    elif relationship_type in get_access_relationship_types():
        return "access"
    elif relationship_type in get_content_relationship_types():
        return "content"
    elif relationship_type in get_user_action_relationship_types():
        return "user_action"
    elif relationship_type in get_version_relationship_types():
        return "version"
    elif relationship_type in get_sync_relationship_types():
        return "sync"
    else:
        return "other"