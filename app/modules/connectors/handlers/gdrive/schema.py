"""
Google Drive Data Schemas

This module defines Pydantic models for Google Drive data structures.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class GoogleDriveConfig(BaseModel):
    """Configuration schema for Google Drive connector."""
    
    organisation_id: str = Field(..., description="Organisation ID for service account lookup")
    timeout_seconds: int = Field(default=30, description="Request timeout in seconds")
    rate_limit_per_hour: int = Field(default=1000, description="Rate limit per hour")
    batch_size: int = Field(default=100, description="Batch size for processing")
    full_sync: bool = Field(default=False, description="Whether to perform full sync")
    
    # Sync configuration
    sync_folders: bool = Field(default=True, description="Whether to sync folders")
    sync_files: bool = Field(default=True, description="Whether to sync files")
    sync_permissions: bool = Field(default=True, description="Whether to sync permissions")
    
    # Content processing
    extract_text: bool = Field(default=True, description="Whether to extract text content")
    generate_embeddings: bool = Field(default=True, description="Whether to generate embeddings")
    chunk_size: int = Field(default=1000, description="Text chunk size for processing")
    
    # Filtering options
    mime_type_filters: Optional[List[str]] = Field(None, description="MIME types to include")
    exclude_mime_types: Optional[List[str]] = Field(None, description="MIME types to exclude")
    max_file_size_mb: Optional[int] = Field(None, description="Maximum file size in MB")
    
    @validator('organisation_id')
    def validate_organisation_id(cls, v):
        if not v or not v.strip():
            raise ValueError('Organisation ID cannot be empty')
        return v.strip()


class GoogleDrivePermission(BaseModel):
    """Schema for Google Drive permissions."""
    
    id: Optional[str] = Field(None, description="Permission ID")
    email_address: Optional[str] = Field(None, description="Email address of the user")
    role: str = Field(..., description="Permission role (owner, editor, viewer)")
    type: str = Field(..., description="Permission type (user, group, domain, anyone)")
    display_name: Optional[str] = Field(None, description="Display name of the user")


class GoogleDriveFile(BaseModel):
    """Schema for Google Drive files."""
    
    id: str = Field(..., description="Google Drive file ID")
    name: str = Field(..., description="File name")
    mime_type: str = Field(..., description="MIME type of the file")
    size: Optional[int] = Field(None, description="File size in bytes")
    web_view_link: Optional[str] = Field(None, description="Web view URL")
    created_time: datetime = Field(..., description="Creation timestamp")
    modified_time: datetime = Field(..., description="Last modification timestamp")
    parents: Optional[List[str]] = Field(None, description="Parent folder IDs")
    permissions: Optional[List[GoogleDrivePermission]] = Field(None, description="File permissions")
    
    # Processing metadata
    vector_id: Optional[str] = Field(None, description="Pinecone vector ID")
    content_hash: Optional[str] = Field(None, description="Content hash for change detection")
    last_processed: Optional[datetime] = Field(None, description="Last processing timestamp")
    processing_status: Optional[str] = Field(None, description="Processing status")
    
    # Extracted content
    text_content: Optional[str] = Field(None, description="Extracted text content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class GoogleDriveFolder(BaseModel):
    """Schema for Google Drive folders."""
    
    id: str = Field(..., description="Google Drive folder ID")
    name: str = Field(..., description="Folder name")
    created_time: datetime = Field(..., description="Creation timestamp")
    modified_time: datetime = Field(..., description="Last modification timestamp")
    parents: Optional[List[str]] = Field(None, description="Parent folder IDs")
    permissions: Optional[List[GoogleDrivePermission]] = Field(None, description="Folder permissions")
    
    # Folder statistics
    file_count: Optional[int] = Field(None, description="Number of files in folder")
    subfolder_count: Optional[int] = Field(None, description="Number of subfolders")
    total_size: Optional[int] = Field(None, description="Total size of folder contents")


class GoogleDriveSearchQuery(BaseModel):
    """Schema for Google Drive search queries."""
    
    query: str = Field(..., description="Search query string")
    mime_type: Optional[str] = Field(None, description="Filter by MIME type")
    folder_id: Optional[str] = Field(None, description="Search within specific folder")
    modified_after: Optional[datetime] = Field(None, description="Modified after timestamp")
    modified_before: Optional[datetime] = Field(None, description="Modified before timestamp")
    owner_email: Optional[str] = Field(None, description="Filter by owner email")
    shared_with_me: Optional[bool] = Field(None, description="Only shared files")
    trashed: bool = Field(default=False, description="Include trashed files")
    limit: int = Field(default=10, ge=1, le=1000, description="Maximum results to return")
    page_token: Optional[str] = Field(None, description="Page token for pagination")


class GoogleDriveSyncResult(BaseModel):
    """Schema for sync operation results."""
    
    sync_id: str = Field(..., description="Unique sync operation ID")
    organisation_id: str = Field(..., description="Organisation ID")
    started_at: datetime = Field(..., description="Sync start time")
    completed_at: Optional[datetime] = Field(None, description="Sync completion time")
    status: str = Field(..., description="Sync status (running, completed, failed)")
    
    # Sync statistics
    files_processed: int = Field(default=0, description="Number of files processed")
    folders_processed: int = Field(default=0, description="Number of folders processed")
    files_created: int = Field(default=0, description="Number of files created")
    files_updated: int = Field(default=0, description="Number of files updated")
    files_deleted: int = Field(default=0, description="Number of files deleted")
    folders_created: int = Field(default=0, description="Number of folders created")
    folders_updated: int = Field(default=0, description="Number of folders updated")
    folders_deleted: int = Field(default=0, description="Number of folders deleted")
    
    # Error tracking
    errors: List[str] = Field(default_factory=list, description="List of errors encountered")
    warnings: List[str] = Field(default_factory=list, description="List of warnings")
    
    # Performance metrics
    total_api_calls: int = Field(default=0, description="Total API calls made")
    total_bytes_processed: int = Field(default=0, description="Total bytes processed")
    average_processing_time: Optional[float] = Field(None, description="Average processing time per item")


class GoogleDriveSearchResult(BaseModel):
    """Schema for search results."""
    
    files: List[GoogleDriveFile] = Field(default_factory=list, description="Found files")
    folders: List[GoogleDriveFolder] = Field(default_factory=list, description="Found folders")
    total_count: int = Field(default=0, description="Total number of results")
    next_page_token: Optional[str] = Field(None, description="Token for next page")
    query_time_ms: float = Field(..., description="Query execution time in milliseconds")
    
    # Search metadata
    query: str = Field(..., description="Original search query")
    filters_applied: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    search_scope: str = Field(default="all", description="Search scope (all, files, folders)")


class GoogleDriveEntityType(str, Enum):
    """Google Drive entity types."""
    FILE = "file"
    FOLDER = "folder"
    DOCUMENT = "document"
    SPREADSHEET = "spreadsheet"
    PRESENTATION = "presentation"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    PDF = "pdf"
    TEXT = "text"
    OTHER = "other"


class GoogleDriveProcessingStatus(str, Enum):
    """Processing status for Google Drive items."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


def get_entity_type_from_mime_type(mime_type: str) -> GoogleDriveEntityType:
    """
    Get entity type from MIME type.
    
    Args:
        mime_type: The MIME type string
        
    Returns:
        GoogleDriveEntityType: The corresponding entity type
    """
    mime_type_mapping = {
        'application/vnd.google-apps.folder': GoogleDriveEntityType.FOLDER,
        'application/vnd.google-apps.document': GoogleDriveEntityType.DOCUMENT,
        'application/vnd.google-apps.spreadsheet': GoogleDriveEntityType.SPREADSHEET,
        'application/vnd.google-apps.presentation': GoogleDriveEntityType.PRESENTATION,
        'application/pdf': GoogleDriveEntityType.PDF,
        'text/plain': GoogleDriveEntityType.TEXT,
    }
    
    # Check for exact matches
    if mime_type in mime_type_mapping:
        return mime_type_mapping[mime_type]
    
    # Check for partial matches
    if mime_type.startswith('image/'):
        return GoogleDriveEntityType.IMAGE
    elif mime_type.startswith('video/'):
        return GoogleDriveEntityType.VIDEO
    elif mime_type.startswith('audio/'):
        return GoogleDriveEntityType.AUDIO
    elif mime_type.startswith('text/'):
        return GoogleDriveEntityType.TEXT
    
    return GoogleDriveEntityType.OTHER


def is_processable_mime_type(mime_type: str) -> bool:
    """
    Check if a MIME type can be processed for text extraction.
    
    Args:
        mime_type: The MIME type string
        
    Returns:
        bool: True if processable, False otherwise
    """
    processable_types = {
        'application/vnd.google-apps.document',
        'application/vnd.google-apps.spreadsheet',
        'application/vnd.google-apps.presentation',
        'application/pdf',
        'text/plain',
        'text/html',
        'text/csv',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    }
    
    return mime_type in processable_types or mime_type.startswith('text/')