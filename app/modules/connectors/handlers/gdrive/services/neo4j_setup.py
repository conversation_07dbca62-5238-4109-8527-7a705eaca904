import structlog
from app.services.neo4j_service import execute_write_query

logger = structlog.get_logger()

def create_google_drive_constraints():
    """
    Create Neo4j constraints for Google Drive entities.
    """
    constraints = [
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (f:GoogleDriveFile)
        REQUIRE f.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (f:GoogleDriveFolder)
        REQUIRE f.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (c:Chunk)
        REQUIRE c.chunk_id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (e:Entity)
        REQUIRE e.entity_id IS UNIQUE
        """
    ]
    
    for query in constraints:
        execute_write_query(query)
    
    logger.info("Google Drive Neo4j constraints created")

def create_google_drive_indexes():
    """
    Create Neo4j indexes for Google Drive entities.
    """
    indexes = [
        """
        CREATE INDEX IF NOT EXISTS FOR (f:GoogleDriveFile)
        ON (f.name)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (f:GoogleDriveFolder)
        ON (f.name)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (f:GoogleDriveFile)
        ON (f.mime_type)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (f:GoogleDriveFile)
        ON (f.vector_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (c:Chunk)
        ON (c.file_id)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (c:Chunk)
        ON (c.chunk_index)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (e:Entity)
        ON (e.name)
        """,
        """
        CREATE INDEX IF NOT EXISTS FOR (e:Entity)
        ON (e.type)
        """
    ]
    
    for query in indexes:
        execute_write_query(query)
    
    logger.info("Google Drive Neo4j indexes created")