from datetime import datetime, timezone
import uuid

class Agent:
    """
    Represents an agent entity in the Neo4j database.
    This is not a SQLAlchemy model but a representation of the Neo4j node.
    """
    def __init__(
        self,
        id: str = None,
        name: str = None,
        description: str = None,
        department_id: str = None,
        owner_id: str = None,
        owner_name: str = None,
        user_ids: list = None,
        created_at: str = None,
        updated_at: str = None,
        visibility: str = "PRIVATE",  # Default to PRIVATE
        status: str = "ACTIVE",      # Default to ACTIVE
        creator_role: str = "MEMBER"  # Default to MEMBER
    ):
        self.id = id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.department_id = department_id
        self.owner_id = owner_id
        self.owner_name = owner_name
        self.user_ids = user_ids or []
        self.created_at = created_at or datetime.now(timezone.utc).isoformat()
        self.updated_at = updated_at or datetime.now(timezone.utc).isoformat()
        self.visibility = visibility
        self.status = status
        self.creator_role = creator_role
    
    @classmethod
    def from_dict(cls, data: dict):
        """
        Create an Agent instance from a dictionary.
        Useful for converting Neo4j node properties to an Agent object.
        """
        return cls(
            id=data.get('id'),
            name=data.get('name'),
            description=data.get('description'),
            department_id=data.get('department_id'),
            owner_id=data.get('owner_id'),
            owner_name=data.get('owner_name'),
            user_ids=data.get('user_ids', []),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at'),
            visibility=data.get('visibility', 'PRIVATE'),
            status=data.get('status', 'ACTIVE'),
            creator_role=data.get('creator_role', 'MEMBER')
        )
    
    def to_dict(self):
        """
        Convert the Agent object to a dictionary.
        Useful for setting Neo4j node properties.
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'department_id': self.department_id,
            'owner_id': self.owner_id,
            'owner_name': self.owner_name,
            'user_ids': self.user_ids,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'visibility': self.visibility,
            'status': self.status,
            'creator_role': self.creator_role
        }
