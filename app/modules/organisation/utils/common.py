import structlog
from typing import List

logger = structlog.get_logger()

class CacheInvalidationService:
    """Shared service for cache invalidation operations."""
    
    @staticmethod
    def invalidate_user_caches(user_ids: List[str]) -> None:
        """
        Invalidate access caches for multiple users.
        
        Args:
            user_ids: List of user IDs to invalidate caches for
        """
        try:
            from app.utils.pinecone.pinecone_service import PineconeService
            pinecone_service = PineconeService()
            
            for user_id in user_ids:
                try:
                    pinecone_service.invalidate_access_cache(user_id)
                except Exception as e:
                    logger.warning(f"Error invalidating cache for user {user_id}: {str(e)}")
        except Exception as e:
            logger.error(f"Error initializing PineconeService for cache invalidation: {str(e)}")

    @staticmethod
    def invalidate_department_cache(department_id: str) -> None:
        """
        Invalidate department access cache.
        
        Args:
            department_id: Department ID to invalidate cache for
        """
        try:
            from app.utils.pinecone.pinecone_service import PineconeService
            pinecone_service = PineconeService()
            
            pinecone_service.invalidate_department_access_cache(department_id)
            logger.info(f"Invalidated department access cache for department {department_id}")
        except Exception as e:
            logger.warning(f"Error invalidating department access cache: {str(e)}")

class PermissionValidator:
    """Shared service for permission validation operations."""
    
    @staticmethod
    def validate_department_access_permission(user_id: str, department_id: str, queries, execute_read_query) -> tuple[bool, str]:
        """
        Validate if user has permission to grant access for a department.
        
        Args:
            user_id: User ID to check
            department_id: Department ID to check
            queries: Query repository instance
            execute_read_query: Query execution function
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Validate department exists
        dept_query = queries.VALIDATE_DEPARTMENT_EXISTS
        dept_params = {"dept_id": department_id}
        dept_result = execute_read_query(dept_query, dept_params)
        
        if not dept_result:
            return False, f"Department with ID {department_id} not found"
        
        # Validate user has permission
        perm_query = queries.VALIDATE_USER_DEPARTMENT_PERMISSION
        perm_params = {
            "user_id": user_id,
            "dept_id": department_id
        }
        perm_result = execute_read_query(perm_query, perm_params)
        
        if not perm_result or (not perm_result[0]['is_member'] and not perm_result[0]['is_admin']):
            return False, f"User does not have permission to grant access for this department"
        
        return True, ""

class ResponseBuilder:
    """Shared utilities for building gRPC responses."""
    
    @staticmethod
    def build_error_response(response_class, error_message: str, **kwargs):
        """
        Build a standardized error response.
        
        Args:
            response_class: The protobuf response class
            error_message: Error message to include
            **kwargs: Additional fields for the response
            
        Returns:
            Error response instance
        """
        return response_class(
            success=False,
            message=error_message,
            **kwargs
        )
    
    @staticmethod
    def build_success_response(response_class, success_message: str, **kwargs):
        """
        Build a standardized success response.
        
        Args:
            response_class: The protobuf response class
            success_message: Success message to include
            **kwargs: Additional fields for the response
            
        Returns:
            Success response instance
        """
        return response_class(
            success=True,
            message=success_message,
            **kwargs
        )

class OrganisationMembershipChecker:
    """Shared service for checking organization membership."""
    
    @staticmethod
    def isUserPartOfAnyOrganisation(user_id: str, execute_read_query, query) -> bool:
        """
        Check if a user is part of any organisation.
        
        Args:
            user_id: The user ID to check
            execute_read_query: Function to execute read queries
            query: The query to execute
            
        Returns:
            bool: True if user is a member of any organisation, False otherwise
        """
        try:
            params = {
                "user_id": user_id
            }
            result = execute_read_query(query, params)
            
            if result and len(result) > 0:
                return result[0].get('is_member_of_any_org', False)
            return False
            
        except Exception as e:
            logger.error("Error checking if user is part of any organisation",
                        user_id=user_id, error=str(e))
            return False