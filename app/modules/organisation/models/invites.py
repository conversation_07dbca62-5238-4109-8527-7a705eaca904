from sqlalchemy import (
    Column, String, DateTime, Boolean, ForeignKey, JSON, Enum as SQLAlchemyEnum
)
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import secrets
import string
from enum import Enum

from app.db.init_db import Base


class InviteStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    EXPIRED = "expired"


def generate_short_token(length=8):
    """Generate a short, unique token for invite URLs"""
    alphabet = string.ascii_letters + string.digits  # a-z, A-Z, 0-9
    return ''.join(secrets.choice(alphabet) for _ in range(length))


class Invite(Base):
    __tablename__ = "invites"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Email of the invitee
    email = Column(String, nullable=False, index=True)
    
    # Org + Dept + Role
    organisation_id = Column(String, nullable=False)
    department = Column(String, nullable=False)
    role = Column(String, nullable=False)
    
    # Optional: permissions as string (previously was JSON array)
    permission = Column(String, nullable=True)
    
    # Who sent the invite (User ID)
    created_by = Column(String, nullable=False)
    
    # Original long token URL
    invite_token = Column(String, nullable=False)
    
    # Short token for URLs (unique, indexed)
    short_token = Column(String(12), nullable=True, unique=True, default=generate_short_token)

    # Status tracking
    status = Column(String, default=InviteStatus.PENDING.value, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    accepted_at = Column(DateTime, nullable=True)
    
    # Meta
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<Invite {self.email} - {self.status}>"
