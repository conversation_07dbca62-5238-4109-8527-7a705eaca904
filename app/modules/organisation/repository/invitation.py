from app.modules.organisation.models.schema_loader import schema
from app.utils.constants.departments import DefaultDepartments, DefaultRoles

class InvitationQueries:
    def __init__(self):
        # Get schema definitions
        self.org_label = schema.get_node_labels()[0]  # "Organisation"
        self.user_label = schema.get_node_labels()[1]  # "User"
        self.dept_label = schema.get_node_labels()[2]  # "Department"
        self.org_dept_rel = schema.get_relationship_types()[1]  # "HAS_DEPARTMENT"
        self.user_dept_rel = schema.get_relationship_types()[2]  # "BELONGS_TO"
        self.subdept_rel = schema.get_relationship_types()[3] # "HAS_SUBDEPARTMENT"

    @property
    def VERIFY_ADMIN_AND_DEPARTMENT(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $organisation_id}})-[:{self.org_dept_rel}]->(admin_dept:{self.dept_label})<-[admin_rel:{self.user_dept_rel}]-(admin_user:{self.user_label} {{id: $user_id}})
            WHERE toLower(admin_dept.name) = toLower($general_dept)
            
            OPTIONAL MATCH (o)-[:{self.org_dept_rel}]->(target_dept:{self.dept_label})
            WHERE toLower(target_dept.name) = toLower($target_dept_name)
            
            OPTIONAL MATCH (target_dept)<-[:{self.user_dept_rel}]-(existing_user:{self.user_label})
            WHERE toLower(existing_user.email) = toLower($target_email)
            
            RETURN 
                admin_user.name as admin_name,
                o.name as org_name,
                admin_rel.role as admin_role,
                target_dept.id as dept_exists,
                existing_user.id as user_exists,
                o, admin_user
        """
    
    @property
    def SETUP_USER_AND_RELATIONSHIPS(self):
        """
        Single query to:
        1. Create user if they don't exist
        2. Validate department exists
        3. Create relationship to target department
        4. Create relationship to General department (if different)
        """
        return f"""
            // Create user if doesn't exist
            MERGE (u:{self.user_label} {{email: $user_email}})
            ON CREATE SET 
                u.name = $user_name,
                u.id = $user_id,
                u.creation_type = $creation_type,
                u.created_at = $timestamp,
                u.updated_at = $timestamp
            ON MATCH SET
                u.id = $user_id,
                u.creation_type = $creation_type,
                u.name = $user_name
            
            WITH u
            
            // Find target department and validate it exists
            MATCH (target_dept:{self.dept_label} {{organisation_id: $org_id}})
            WHERE toLower(target_dept.name) = toLower($department_name)
            
            // Create relationship to target department
            MERGE (u)-[:{self.user_dept_rel} {{
                role: $role, 
                joined_at: $timestamp, 
                invited_by: $inviter, 
                permission: $permission
            }}]->(target_dept)
            
            WITH u, target_dept
            
            // Handle General department relationship if target is not General
            OPTIONAL MATCH (general_dept:{self.dept_label} {{name: $general_dept_name, organisation_id: $org_id}})
            WHERE toLower($department_name) <> toLower($general_dept_name)

            OPTIONAL MATCH (u)-[existing_rel:{self.user_dept_rel}]->(general_dept)

            // Only create General relationship if it doesn't exist
            FOREACH (_ IN CASE WHEN general_dept IS NOT NULL AND existing_rel IS NULL THEN [1] ELSE [] END |
                MERGE (u)-[:{self.user_dept_rel} {{
                    role: $general_role, 
                    joined_at: $timestamp, 
                    invited_by: $inviter, 
                    permission: $general_permission
                }}]->(general_dept)
            )
            
            RETURN target_dept.name as target_department, 
                CASE WHEN general_dept IS NOT NULL AND existing_rel IS NULL THEN 1 ELSE 0 END as general_added
        """

    @property
    def VALIDATE_DEPARTMENT_EXISTS(self):
        """Quick validation query to check if department exists before processing"""
        return f"""
            MATCH (d:{self.dept_label} {{organisation_id: $org_id}})
            WHERE toLower(d.name) = toLower($department_name)
            RETURN d.name as department_name
        """
    
    @property
    def FETCH_ORGS_DETAILS(self):
        return f"""
            UNWIND $org_ids AS org_id
            MATCH (o:{self.org_label} {{id: org_id}})
            RETURN o.id as id, o
        """
    
    @property
    def FETCH_USERS_DETAILS(self):
        return f"""
            UNWIND $user_ids AS user_id
            MATCH (u:{self.user_label} {{id: user_id}})
            RETURN u.id as id, u.name as name
        """
        
    @property
    def FETCH_ACCEPTED_INVITES_BY_INVITER(self):
        """Query to fetch all accepted invitations where the user is the inviter"""
        return f"""
            // Match users who were invited by this user
            MATCH (invitee:{self.user_label})-[rel:{self.user_dept_rel}]->(dept:{self.dept_label}{{name: $dept_name}})<-[:{self.org_dept_rel}]-(org:{self.org_label}{{id: $org_id}})
            WHERE rel.invited_by IS NOT NULL AND rel.invited_by <> ""
            // WHERE rel.invited_by = $user_id
            
            // Return details about the invitee, organization and department
            RETURN
                invitee.id as invitee_id,
                invitee.name as invitee_name,
                invitee.email as invitee_email,
                org.id as org_id,
                org.name as org_name,
                dept.name as department_name,
                rel.role as role,
                rel.permission as permission,
                rel.joined_at as joined_at
            ORDER BY rel.joined_at DESC
        """