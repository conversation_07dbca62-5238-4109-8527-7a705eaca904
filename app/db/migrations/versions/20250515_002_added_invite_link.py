"""initial

Revision ID: 20250515_002
Revises: 
Create Date: 2025-05-14 20:12:17.855146+00:00

"""
from alembic import op
import sqlalchemy as sa
from enum import Enum


# revision identifiers, used by Alembic.
revision = '20250515_002'
down_revision = '20250515_001'
branch_labels = None
depends_on = None


class InviteStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    EXPIRED = "expired"


def upgrade() -> None:
    # Skip creating the enum type since it already exists from the previous migration
    
    op.create_table(
        'invites',
        sa.Column('id', sa.String(), primary_key=True),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('organization_id', sa.String(), nullable=False),
        sa.Column('department', sa.String(), nullable=True),
        sa.Column('role', sa.String(), nullable=True),
        sa.Column('invite_link', sa.String(), nullable=False),
        sa.Column('permissions', sa.JSON(), nullable=True),
        sa.Column('created_by', sa.String(), nullable=False),
        # Use the existing enum type by referencing it directly
        sa.Column('status', sa.Enum('pending', 'accepted', 'expired', name='invitestatus', create_type=False),
                  nullable=False, server_default='pending'),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('accepted_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_invites_email', 'invites', ['email'])
    op.create_index('idx_invites_org', 'invites', ['organization_id'])


def downgrade() -> None:
    op.drop_index('idx_invites_email')
    op.drop_index('idx_invites_org')
    op.drop_table('invites')
    
    # We don't drop the enum type in downgrade as it might be used elsewhere
    # If you want to drop it, uncomment the following:
    # op.execute('DROP TYPE IF EXISTS invitestatus')