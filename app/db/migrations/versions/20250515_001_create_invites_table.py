"""create invites table

Revision ID: 20250515_001
Revises: 
Create Date: 2025-05-15 00:37:00.000000

"""
from alembic import op
import sqlalchemy as sa
from enum import Enum
from sqlalchemy import text
import uuid
from datetime import datetime


# revision identifiers, used by Alembic.
revision = '20250515_001'
down_revision = None
branch_labels = None
depends_on = None


class InviteStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    EXPIRED = "expired"


def upgrade() -> None:
    # Create enum type first
    invitestatus = sa.Enum('pending', 'accepted', 'expired', name='invitestatus')
    invitestatus.create(op.get_bind())
    
    op.create_table(
        'invites',
        sa.Column('id', sa.String(), primary_key=True),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('organization_id', sa.String(), nullable=False),
        sa.Column('department', sa.String(), nullable=True),
        sa.Column('role', sa.String(), nullable=True),
        sa.Column('permissions', sa.JSON(), nullable=True),
        sa.Column('created_by', sa.String(), nullable=False),
        sa.Column('status', sa.Enum('pending', 'accepted', 'expired', name='invitestatus'),
                  nullable=False, server_default='pending'),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('accepted_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_invites_email', 'invites', ['email'])
    op.create_index('idx_invites_org', 'invites', ['organization_id'])


def downgrade() -> None:
    op.drop_index('idx_invites_email')
    op.drop_index('idx_invites_org')
    op.drop_table('invites')