"""
Kafka database connection module.
"""
import logging
from typing import Optional
from kafka import <PERSON>fkaProducer, KafkaAdminClient
from app.core.config import settings
import json

logger = logging.getLogger(__name__)


class KafkaConnectionManager:
    """
    Manages Kafka connections.
    """
    _producer: Optional[KafkaProducer] = None
    _admin_client: Optional[KafkaAdminClient] = None

    @classmethod
    def get_producer(cls) -> KafkaProducer:
        """
        Returns a singleton instance of the Kafka producer.
        
        Returns:
            KafkaProducer: Kafka producer instance
        """
        if cls._producer is None:
            try:
                cls._producer = KafkaProducer(
                    bootstrap_servers=settings.BOOTSTRAP_SERVERS,
                    value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                    key_serializer=lambda k: str(k).encode("utf-8"),
                    acks="all",  # Wait for all replicas
                    retries=3,  # Retry on failure
                    linger_ms=5,  # Small batching delay
                )
                logger.info("Kafka producer initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing Kafka producer: {str(e)}")
                raise
        return cls._producer

    @classmethod
    def get_admin_client(cls) -> KafkaAdminClient:
        """
        Returns a singleton instance of the Kafka admin client.
        
        Returns:
            KafkaAdminClient: Kafka admin client instance
        """
        if cls._admin_client is None:
            try:
                cls._admin_client = KafkaAdminClient(
                    bootstrap_servers=settings.BOOTSTRAP_SERVERS
                )
                logger.info("Kafka admin client initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing Kafka admin client: {str(e)}")
                raise
        return cls._admin_client

    @classmethod
    def close_connections(cls) -> None:
        """
        Closes all Kafka connections.
        """
        if cls._producer is not None:
            cls._producer.close()
            cls._producer = None
            logger.info("Kafka producer connection closed")

        if cls._admin_client is not None:
            cls._admin_client.close()
            cls._admin_client = None
            logger.info("Kafka admin client connection closed")


def get_kafka_producer() -> KafkaProducer:
    """
    Get Kafka producer instance.
    
    Returns:
        KafkaProducer: Kafka producer instance
    """
    return KafkaConnectionManager.get_producer()


def get_kafka_admin_client() -> KafkaAdminClient:
    """
    Get Kafka admin client instance.
    
    Returns:
        KafkaAdminClient: Kafka admin client instance
    """
    return KafkaConnectionManager.get_admin_client()