"""
Pinecone database connection module.
"""
from app.core.config import settings
from typing import Optional
import structlog
from pinecone import Pinecone, ServerlessSpec

logger = structlog.get_logger()


class PineconeConnectionManager:
    """
    Manages a singleton Pinecone connection.
    """
    _client: Optional[Pinecone] = None
    _index = None
    _dimension: Optional[int] = None

    @classmethod
    def get_client(cls, dimension: Optional[int] = None) -> Optional[Pinecone]:
        """
        Returns a singleton instance of the Pinecone client.
        
        Args:
            dimension: Vector dimension for index creation
            
        Returns:
            Pinecone: Pinecone client instance or None if not configured
        """
        if cls._client is None:
            api_key = settings.PINECONE_API_KEY
            if not api_key:
                logger.warning("PINECONE_API_KEY not set. Pinecone client will not be initialized.")
                return None
            
            try:
                cls._client = Pinecone(api_key=api_key)
                
                # Store dimension for index operations
                if dimension is None:
                    try:
                        from app.utils.sentence_transformers import get_sentence_transformer_service
                        sentence_service = get_sentence_transformer_service()
                        dimension = sentence_service.get_embedding_dimension()
                        logger.info(f"Using Sentence Transformers dimension: {dimension}")
                    except Exception as e:
                        logger.warning(f"Could not get dimension from Sentence Transformers: {e}")
                        dimension = 768  # Default to Sentence Transformers standard dimension
                
                cls._dimension = dimension
                logger.info("Pinecone client initialized successfully")
                
            except Exception as e:
                logger.error(f"Error initializing Pinecone client: {str(e)}")
                cls._client = None
                
        return cls._client

    @classmethod
    def get_index(cls, dimension: Optional[int] = None):
        """
        Get or create Pinecone index.
        
        Args:
            dimension: Vector dimension for index creation
            
        Returns:
            Pinecone Index instance or None
        """
        client = cls.get_client(dimension)
        if not client:
            return None
            
        if cls._index is None:
            index_name = settings.PINECONE_INDEX_NAME
            
            try:
                # Check if index exists, create if it doesn't
                existing_indexes = [index.name for index in client.list_indexes()]
                
                if index_name not in existing_indexes:
                    logger.info(f"Creating Pinecone index: {index_name} with {cls._dimension} dimensions")
                    client.create_index(
                        name=index_name,
                        dimension=cls._dimension,
                        metric="cosine",
                        spec=ServerlessSpec(
                            cloud="aws",
                            region="us-east-1"
                        )
                    )
                
                cls._index = client.Index(index_name)
                logger.info(f"Pinecone index initialized: {index_name}")
                
            except Exception as e:
                logger.error(f"Error initializing Pinecone index: {str(e)}")
                cls._index = None
                
        return cls._index

    @classmethod
    def is_initialized(cls) -> bool:
        """
        Check if the Pinecone client and index are initialized.
        
        Returns:
            Boolean indicating if the client is initialized
        """
        return cls._client is not None and cls._index is not None

    @classmethod
    def get_dimension(cls) -> Optional[int]:
        """
        Get the configured dimension.
        
        Returns:
            Configured dimension or None
        """
        return cls._dimension


def get_pinecone_client(dimension: Optional[int] = None) -> Optional[Pinecone]:
    """
    Get Pinecone client instance.
    
    Args:
        dimension: Vector dimension for index creation
        
    Returns:
        Pinecone client instance or None
    """
    return PineconeConnectionManager.get_client(dimension)


def get_pinecone_index(dimension: Optional[int] = None):
    """
    Get Pinecone index instance.
    
    Args:
        dimension: Vector dimension for index creation
        
    Returns:
        Pinecone Index instance or None
    """
    return PineconeConnectionManager.get_index(dimension)