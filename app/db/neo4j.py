"""
Neo4j database connection module.
"""
import logging
from typing import Generator

from neo4j import GraphDatabase, Driver, Session, Transaction
from contextlib import contextmanager

from app.core.config import settings

logger = logging.getLogger(__name__)


class Neo4jSessionManager:
    """
    Manages a singleton Neo4j driver.
    """
    _driver: Driver = None

    @classmethod
    def get_driver(cls) -> Driver:
        """
        Returns a singleton instance of the Neo4j driver.
        """
        if cls._driver is None:
            try:
                cls._driver = GraphDatabase.driver(
                    settings.NEO4J_URI,
                    auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
                )
                cls._driver.verify_connectivity()
                logger.info("Connected to Neo4j database")
            except Exception as e:
                logger.error(f"Failed to connect to Neo4j: {e}")
                raise
        return cls._driver

    @classmethod
    def close_driver(cls) -> None:
        """
        Closes the Neo4j driver connection.
        """
        if cls._driver is not None:
            cls._driver.close()
            cls._driver = None
            logger.info("Neo4j connection closed")


@contextmanager
def get_neo4j_session() -> Generator[Session, None, None]:
    """
    Dependency-injection friendly generator for Neo4j session.
    """
    driver = Neo4jSessionManager.get_driver()
    session = driver.session(database=settings.NEO4j_DATABASE)
    try:
        yield session
    finally:
        session.close()


@contextmanager
def get_neo4j_transaction() -> Generator[Transaction, None, None]:
    """
    Provides a transactional context for executing multiple write operations.
    Rolls back if any exception occurs.
    """
    session = Neo4jSessionManager.get_driver().session(database=settings.NEO4j_DATABASE)
    tx = session.begin_transaction()
    try:
        yield tx  # allow user code to run multiple tx.run(...)
        tx.commit()
        logger.debug("Transaction committed successfully")
    except Exception as e:
        tx.rollback()
        logger.error(f"Transaction rolled back due to error: {e}")
        raise
    finally:
        session.close()
